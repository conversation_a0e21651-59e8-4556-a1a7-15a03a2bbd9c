using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using ProjetoIntegrador.Classes;

namespace ProjetoIntegrador.Coordenador.C_ConferirGrade2
{
    public partial class C_ConferirGrade2 : Form
    {
        private comandosbanco cmdBanco = new comandosbanco();
        private DataTable dadosGrade;
        private Dictionary<string, Label> labelsGrade = new Dictionary<string, Label>();

        public C_ConferirGrade2()
        {
            InitializeComponent(); // Inicializa os componentes do formulário
            InicializarComponentes();
            CarregarCursos();
        }

        private void label7_Click(object sender, EventArgs e)
        {
            // Evento vazio - pode ser removido se não for necessário
        }

        private void btn_fechar_apresentaconferirgrade_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
               "Deseja realmente fechar a aplicação?",
               "Confirmação",
               MessageBoxButtons.YesNo,
               MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto

        }

        private void btn_minimizar_apresentaconferirgrade_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_apresentaconferirgrade_Click(object sender, EventArgs e)
        {
            ToggleMaximizeWindow();
        }

        /// <summary>
        /// Alterna entre maximizar e restaurar a janela, centralizando quando restaura
        /// </summary>
        private void ToggleMaximizeWindow()
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                // Se está maximizada, restaura ao tamanho normal e centraliza
                this.WindowState = FormWindowState.Normal;
                this.StartPosition = FormStartPosition.CenterScreen;
                this.CenterToScreen();
            }
            else
            {
                // Se não está maximizada, maximiza
                this.WindowState = FormWindowState.Maximized;
            }
        }

        /// <summary>
        /// Inicializa os componentes dinâmicos da grade
        /// </summary>
        private void InicializarComponentes()
        {
            // Configurar eventos dos ComboBoxes
            cb_selecionacurso_conferirgrade.SelectedIndexChanged += Cb_selecionacurso_SelectedIndexChanged;
            cb_selecionarsemestre_conferirgrade.SelectedIndexChanged += Cb_selecionarsemestre_SelectedIndexChanged;

            // Criar labels dinâmicos para a grade
            CriarLabelsGrade();
        }

        /// <summary>
        /// Cria os labels dinâmicos para exibir as disciplinas na grade
        /// </summary>
        private void CriarLabelsGrade()
        {
            try
            {
                // Limpar controles existentes (exceto cabeçalhos)
                labelsGrade.Clear();

                // Definir os horários e dias conforme especificado
                string[] horarios = { "19:00-20:30", "21:00-23:00" };
                string[] dias = { "Segunda", "Terça", "Quarta", "Quinta", "Sexta" };

                // Verificar se o TableLayoutPanel existe
                if (pnl_grade1 == null)
                {
                    MessageBox.Show("TableLayoutPanel não encontrado!", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Configurar o TableLayoutPanel se necessário
                if (pnl_grade1.RowCount < 3) // Cabeçalho + 2 horários
                {
                    pnl_grade1.RowCount = 3;
                }
                if (pnl_grade1.ColumnCount < 6) // Coluna hora + 5 dias
                {
                    pnl_grade1.ColumnCount = 6;
                }

                // Criar labels para cada célula da grade (excluindo cabeçalhos)
                for (int linha = 1; linha <= 2; linha++) // 2 horários (linha 0 é cabeçalho)
                {
                    for (int coluna = 1; coluna <= 5; coluna++) // 5 dias (coluna 0 é hora)
                    {
                        Label lblCelula = new Label();
                        lblCelula.Dock = DockStyle.Fill;
                        lblCelula.TextAlign = ContentAlignment.MiddleCenter;
                        lblCelula.BackColor = Color.White;
                        lblCelula.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                        lblCelula.Padding = new Padding(5);
                        lblCelula.AutoSize = false;
                        lblCelula.BorderStyle = BorderStyle.FixedSingle;
                        lblCelula.Margin = new Padding(1);
                        lblCelula.ForeColor = Color.Black;

                        // Adicionar ao TableLayoutPanel
                        pnl_grade1.Controls.Add(lblCelula, coluna, linha);

                        // Adicionar ao dicionário para fácil acesso
                        string chave = $"{dias[coluna - 1]}_{horarios[linha - 1]}";
                        labelsGrade[chave] = lblCelula;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao criar labels da grade: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Carrega os cursos no ComboBox
        /// </summary>
        private void CarregarCursos()
        {
            try
            {
                DataTable cursos = cmdBanco.ConsultarCursos();
                cb_selecionacurso_conferirgrade.DataSource = cursos;
                cb_selecionacurso_conferirgrade.DisplayMember = "NOME_CURSO";
                cb_selecionacurso_conferirgrade.ValueMember = "ID_CURSO";
                cb_selecionacurso_conferirgrade.SelectedIndex = -1;

                // Carregar semestres (1 a 8)
                for (int i = 1; i <= 8; i++)
                {
                    cb_selecionarsemestre_conferirgrade.Items.Add(i.ToString());
                }
                cb_selecionarsemestre_conferirgrade.SelectedIndex = -1;
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar cursos: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Evento quando o curso é selecionado
        /// </summary>
        private void Cb_selecionacurso_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cb_selecionacurso_conferirgrade.SelectedIndex >= 0)
            {
                lbl_nomecurso.Text = cb_selecionacurso_conferirgrade.Text;
                CarregarGrade();
            }
        }

        /// <summary>
        /// Evento quando o semestre é selecionado
        /// </summary>
        private void Cb_selecionarsemestre_SelectedIndexChanged(object sender, EventArgs e)
        {
            CarregarGrade();
        }

        /// <summary>
        /// Carrega os dados da grade horária
        /// </summary>
        private void CarregarGrade()
        {
            if (cb_selecionacurso_conferirgrade.SelectedIndex < 0 || cb_selecionarsemestre_conferirgrade.SelectedIndex < 0)
                return;

            try
            {
                int idCurso = Convert.ToInt32(cb_selecionacurso_conferirgrade.SelectedValue);
                int semestre = Convert.ToInt32(cb_selecionarsemestre_conferirgrade.Text);

                // Consultar disciplinas do curso e semestre baseadas nos horários de disponibilidade
                dadosGrade = ConsultarGradeHoraria(idCurso, semestre);

                // Debug: Mostrar quantos registros foram encontrados
                string mensagemDebug = $"Curso: {cb_selecionacurso_conferirgrade.Text}\n" +
                                     $"Semestre: {semestre}\n" +
                                     $"Registros encontrados: {dadosGrade?.Rows.Count ?? 0}";

                // Opcional: Descomentar para debug
                // MessageBox.Show(mensagemDebug, "Debug - Dados Carregados", MessageBoxButtons.OK, MessageBoxIcon.Information);

                ExibirGrade();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar grade: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Consulta os dados da grade horária
        /// </summary>
        private DataTable ConsultarGradeHoraria(int idCurso, int semestre)
        {
            return cmdBanco.ConsultarGradeHoraria(idCurso, semestre);
        }

        /// <summary>
        /// Exibe os dados na grade horária baseada nos horários de disponibilidade dos professores
        /// Formato: linha 1 (19:00-20:30) e linha 2 (21:00-23:00)
        /// Cada célula: nome_disciplina + nome_professor
        /// </summary>
        private void ExibirGrade()
        {
            // Limpar todas as células
            foreach (var label in labelsGrade.Values)
            {
                label.Text = "";
                label.BackColor = Color.White;
                label.ForeColor = Color.Black;
            }

            if (dadosGrade == null || dadosGrade.Rows.Count == 0)
            {
                // Mostrar mensagem se não há dados
                if (labelsGrade.Count > 0)
                {
                    var primeiraLabel = labelsGrade.Values.First();
                    primeiraLabel.Text = "Nenhuma disciplina\nencontrada para\neste curso/semestre";
                    primeiraLabel.ForeColor = Color.Gray;
                    primeiraLabel.Font = new Font("Segoe UI", 8F, FontStyle.Italic);
                }
                return;
            }

            // Mapear dias da semana para o formato usado na grade
            Dictionary<string, string> mapaDias = new Dictionary<string, string>
            {
                { "Segunda-feira", "Segunda" },
                { "Terça-feira", "Terça" },
                { "Quarta-feira", "Quarta" },
                { "Quinta-feira", "Quinta" },
                { "Sexta-feira", "Sexta" },
                { "Segunda", "Segunda" },
                { "Terça", "Terça" },
                { "Quarta", "Quarta" },
                { "Quinta", "Quinta" },
                { "Sexta", "Sexta" }
            };

            // Preencher a grade com os dados conforme disponibilidade do professor
            foreach (DataRow row in dadosGrade.Rows)
            {
                if (row["DIA_SEMANA"] == DBNull.Value || row["HORARIO_INICIO"] == DBNull.Value)
                    continue;

                string diaSemana = row["DIA_SEMANA"]?.ToString() ?? "";
                if (string.IsNullOrEmpty(diaSemana))
                    continue;

                TimeSpan horarioInicio;
                if (!TimeSpan.TryParse(row["HORARIO_INICIO"]?.ToString(), out horarioInicio))
                    continue;

                // Mapear dia da semana
                if (!mapaDias.ContainsKey(diaSemana))
                    continue;

                string diaFormatado = mapaDias[diaSemana];

                // Determinar o período baseado no horário de disponibilidade do professor
                // Linha 1: 19:00 às 20:30 (período 1)
                // Linha 2: 21:00 às 23:00 (período 2)
                string horarioFormatado = "";
                if (horarioInicio >= TimeSpan.Parse("19:00") && horarioInicio <= TimeSpan.Parse("20:30"))
                {
                    horarioFormatado = "19:00-20:30";
                }
                else if (horarioInicio >= TimeSpan.Parse("21:00") && horarioInicio <= TimeSpan.Parse("23:00"))
                {
                    horarioFormatado = "21:00-23:00";
                }

                if (string.IsNullOrEmpty(horarioFormatado))
                    continue;

                // Encontrar a célula correspondente na grade
                string chave = $"{diaFormatado}_{horarioFormatado}";
                if (labelsGrade.ContainsKey(chave))
                {
                    string disciplina = row["NOME_DISCIPLINA"]?.ToString() ?? "Disciplina não informada";
                    string professor = row["NOME_PROFESSOR"]?.ToString() ?? "Professor não informado";

                    // Formato especificado: nome_disciplina + nome_professor
                    string textoCompleto = $"{disciplina}\n{professor}";

                    labelsGrade[chave].Text = textoCompleto;
                    labelsGrade[chave].BackColor = Color.LightBlue;
                    labelsGrade[chave].ForeColor = Color.DarkBlue;
                    labelsGrade[chave].Font = new Font("Segoe UI", 8F, FontStyle.Regular);
                    labelsGrade[chave].TextAlign = ContentAlignment.MiddleCenter;
                }
            }
        }

        private void btn_voltar_apresentaconferirgrade_Click(object sender, EventArgs e)
        {
            // Cria uma nova instância da tela principal e a exibe
            ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal telaPrincipal = new ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal();
            telaPrincipal.Show();
            this.Hide(); // Oculta a tela atual em vez de fechá-la
        }
    }
}