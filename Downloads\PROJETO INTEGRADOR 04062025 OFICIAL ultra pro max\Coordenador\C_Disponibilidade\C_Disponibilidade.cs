﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using ProjetoIntegrador.Classes;

namespace ProjetoIntegrador.Coordenador.C_Disponibilidade
{
    public partial class C_Disponibilidade : Form
    {
        private comandosbanco cmdBanco = new comandosbanco();
        private DataTable dadosDisponibilidades;
        private int paginaAtual = 0;
        private int registrosPorPagina = 9;
        private int totalPaginas = 0;
        private bool modoEdicao = false;
        private List<Label> labelsProfessor = new List<Label>();
        private List<Label> labelsDias = new List<Label>();
        private List<Label> labelsHorario = new List<Label>();
        private List<TextBox> textBoxesEdicao = new List<TextBox>();
        private List<CheckBox> checkBoxes = new List<CheckBox>();
        private HashSet<int> itensSelecionadosGlobal = new HashSet<int>(); // Para manter seleções entre páginas

        public C_Disponibilidade()
        {
            InitializeComponent();
            InicializarComponentes();
            CarregarDisponibilidades();
        }

        /// <summary>
        /// Inicializa os componentes necessários para o funcionamento do formulário
        /// </summary>
        private void InicializarComponentes()
        {
            // Criar checkboxes dinamicamente
            CriarCheckboxes();

            // Criar labels para exibir dados
            CriarLabelsParaDados();
        }

        /// <summary>
        /// Cria checkboxes dinâmicos para seleção de registros
        /// </summary>
        private void CriarCheckboxes()
        {
            for (int i = 0; i < registrosPorPagina; i++)
            {
                CheckBox cb = new CheckBox();
                cb.AutoSize = true;
                cb.BackColor = Color.Transparent;
                cb.Location = new Point(10, 10); // Será ajustado pelo TableLayoutPanel
                cb.Name = $"cb_linha{i + 1}_disponibilidade";
                cb.Size = new Size(15, 14);
                cb.TabIndex = i + 50;
                cb.UseVisualStyleBackColor = false;
                cb.Visible = false;

                // Adicionar ao painel na primeira coluna (antes dos dados)
                // Vamos criar uma nova coluna para os checkboxes
                checkBoxes.Add(cb);
                this.Controls.Add(cb);
            }
        }

        /// <summary>
        /// Cria labels dinâmicos para exibir os dados de disponibilidade
        /// </summary>
        private void CriarLabelsParaDados()
        {
            for (int i = 0; i < registrosPorPagina; i++)
            {
                // Labels para Professor
                Label lblProfessor = new Label();
                lblProfessor.AutoSize = false;
                lblProfessor.Size = new Size(280, 40);
                lblProfessor.TextAlign = ContentAlignment.MiddleLeft;
                lblProfessor.Padding = new Padding(25, 0, 0, 0);
                lblProfessor.BackColor = Color.Transparent;
                lblProfessor.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                lblProfessor.DoubleClick += (s, e) => IniciarEdicao(s as Label, "NOME_PROFESSOR");
                pnl_disponibilidade.Controls.Add(lblProfessor, 0, i + 1);
                labelsProfessor.Add(lblProfessor);

                // Labels para Dias
                Label lblDias = new Label();
                lblDias.AutoSize = false;
                lblDias.Size = new Size(280, 40);
                lblDias.TextAlign = ContentAlignment.MiddleCenter;
                lblDias.BackColor = Color.Transparent;
                lblDias.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                lblDias.DoubleClick += (s, e) => IniciarEdicao(s as Label, "DIA_SEMANA");
                pnl_disponibilidade.Controls.Add(lblDias, 1, i + 1);
                labelsDias.Add(lblDias);

                // Labels para Horário
                Label lblHorario = new Label();
                lblHorario.AutoSize = false;
                lblHorario.Size = new Size(280, 40);
                lblHorario.TextAlign = ContentAlignment.MiddleCenter;
                lblHorario.BackColor = Color.Transparent;
                lblHorario.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                lblHorario.DoubleClick += (s, e) => IniciarEdicao(s as Label, "HORARIO");
                pnl_disponibilidade.Controls.Add(lblHorario, 2, i + 1);
                labelsHorario.Add(lblHorario);
            }
        }

        /// <summary>
        /// Carrega as disponibilidades do banco de dados
        /// </summary>
        private void CarregarDisponibilidades()
        {
            try
            {
                dadosDisponibilidades = cmdBanco.ConsultarTodasDisponibilidadesComProfessores();
                totalPaginas = (int)Math.Ceiling((double)dadosDisponibilidades.Rows.Count / registrosPorPagina);
                if (totalPaginas == 0) totalPaginas = 1;

                ExibirDadosPagina();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar disponibilidades: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Exibe os dados da página atual
        /// </summary>
        private void ExibirDadosPagina()
        {
            // Limpar todos os labels
            foreach (var label in labelsProfessor.Concat(labelsDias).Concat(labelsHorario))
            {
                label.Text = "";
                label.Tag = null;
            }

            // Salvar seleções da página atual antes de trocar
            SalvarSelecoesPaginaAtual();

            // Ocultar todos os checkboxes
            foreach (var cb in checkBoxes)
            {
                cb.Visible = false;
                cb.Checked = false;
            }

            if (dadosDisponibilidades == null || dadosDisponibilidades.Rows.Count == 0)
                return;

            int inicioIndice = paginaAtual * registrosPorPagina;
            int fimIndice = Math.Min(inicioIndice + registrosPorPagina, dadosDisponibilidades.Rows.Count);

            for (int i = inicioIndice; i < fimIndice; i++)
            {
                int indiceLabel = i - inicioIndice;
                DataRow row = dadosDisponibilidades.Rows[i];

                // Preencher dados nos labels
                labelsProfessor[indiceLabel].Text = row["NOME_PROFESSOR"].ToString();
                labelsProfessor[indiceLabel].Tag = row;

                labelsDias[indiceLabel].Text = row["DIA_SEMANA"].ToString();
                labelsDias[indiceLabel].Tag = row;

                string horarioInicio = row["HORARIO_INICIO"].ToString();
                string horarioFim = row["HORARIO_FIM"].ToString();
                labelsHorario[indiceLabel].Text = $"{horarioInicio} - {horarioFim}";
                labelsHorario[indiceLabel].Tag = row;

                // Configurar checkbox
                checkBoxes[indiceLabel].Visible = true;
                checkBoxes[indiceLabel].Tag = row;

                // Restaurar seleção se estava selecionado globalmente
                int idDisponibilidade = Convert.ToInt32(row["ID_DISPONIBILIDADE"]);
                checkBoxes[indiceLabel].Checked = itensSelecionadosGlobal.Contains(idDisponibilidade);

                // Posicionar checkbox (ajustar conforme layout)
                int yPosition = 160 + (indiceLabel * 45); // Ajustar conforme necessário
                checkBoxes[indiceLabel].Location = new Point(180, yPosition);
            }

            AtualizarControlePaginacao();
        }

        /// <summary>
        /// Salva as seleções da página atual no conjunto global
        /// </summary>
        private void SalvarSelecoesPaginaAtual()
        {
            foreach (var cb in checkBoxes)
            {
                if (cb.Visible && cb.Tag != null)
                {
                    DataRow row = (DataRow)cb.Tag;
                    int idDisponibilidade = Convert.ToInt32(row["ID_DISPONIBILIDADE"]);

                    if (cb.Checked)
                    {
                        itensSelecionadosGlobal.Add(idDisponibilidade);
                    }
                    else
                    {
                        itensSelecionadosGlobal.Remove(idDisponibilidade);
                    }
                }
            }
        }

        /// <summary>
        /// Obtém todos os itens selecionados de todas as páginas
        /// </summary>
        private List<DataRow> ObterTodosItensSelecionados()
        {
            // Salvar seleções da página atual
            SalvarSelecoesPaginaAtual();

            // Buscar todas as linhas selecionadas no DataTable
            var itensSelecionados = new List<DataRow>();

            foreach (DataRow row in dadosDisponibilidades.Rows)
            {
                int idDisponibilidade = Convert.ToInt32(row["ID_DISPONIBILIDADE"]);
                if (itensSelecionadosGlobal.Contains(idDisponibilidade))
                {
                    itensSelecionados.Add(row);
                }
            }

            return itensSelecionados;
        }

        /// <summary>
        /// Atualiza os controles de paginação
        /// </summary>
        private void AtualizarControlePaginacao()
        {
            lbl_paginacao.Text = $"Página {paginaAtual + 1} de {totalPaginas}";
            btn_anterior_paginacao.Enabled = paginaAtual > 0;
            btn_proximo_paginacao.Enabled = paginaAtual < totalPaginas - 1;
        }

        private void btn_disciplinas_Click(object sender, EventArgs e)
        {

        }

        private void pnl_lado_C1_Paint(object sender, PaintEventArgs e)
        {

        }

        private void btn_voltar_disponibilidade_Click(object sender, EventArgs e)
        {
            // Cria uma nova instância da tela principal e a exibe
            ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal telaPrincipal = new ProjetoIntegrador.Coordenador.C_TelaPrincipal.C_TelaPrincipal();
            telaPrincipal.Show();
            this.Hide(); // Oculta a tela atual em vez de fechá-la
        }

        private void btn_minimizar_disponibilidade_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_disponibilidade_Click(object sender, EventArgs e)
        {
            ToggleMaximizeWindow();
        }

        /// <summary>
        /// Alterna entre maximizar e restaurar a janela, centralizando quando restaura
        /// </summary>
        private void ToggleMaximizeWindow()
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                // Se está maximizada, restaura ao tamanho normal e centraliza
                this.WindowState = FormWindowState.Normal;
                this.StartPosition = FormStartPosition.CenterScreen;
                this.CenterToScreen();
            }
            else
            {
                // Se não está maximizada, maximiza
                this.WindowState = FormWindowState.Maximized;
            }
        }

        private void btn_fechar_disponibilidade_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto

        }

        /// <summary>
        /// Navega para a página anterior
        /// </summary>
        private void btn_anterior_paginacao_Click(object sender, EventArgs e)
        {
            if (paginaAtual > 0)
            {
                paginaAtual--;
                ExibirDadosPagina();
            }
        }

        /// <summary>
        /// Navega para a próxima página
        /// </summary>
        private void btn_proximo_paginacao_Click(object sender, EventArgs e)
        {
            if (paginaAtual < totalPaginas - 1)
            {
                paginaAtual++;
                ExibirDadosPagina();
            }
        }

        /// <summary>
        /// Inicia o modo de edição para um campo específico
        /// </summary>
        private void IniciarEdicao(Label label, string campo)
        {
            if (!modoEdicao || label?.Tag == null) return;

            // Criar TextBox para edição
            TextBox txtEdicao = new TextBox();
            txtEdicao.Text = label.Text;
            txtEdicao.Size = label.Size;
            txtEdicao.Location = label.Location;
            txtEdicao.Font = label.Font;
            txtEdicao.BackColor = Color.White;
            txtEdicao.BorderStyle = BorderStyle.FixedSingle;

            // Armazenar informações no Tag
            txtEdicao.Tag = new { Label = label, Campo = campo, Row = (DataRow)label.Tag };

            // Eventos do TextBox
            txtEdicao.KeyDown += (s, e) =>
            {
                if (e.KeyCode == Keys.Enter)
                {
                    FinalizarEdicao(txtEdicao);
                }
                else if (e.KeyCode == Keys.Escape)
                {
                    CancelarEdicao(txtEdicao);
                }
            };

            txtEdicao.LostFocus += (s, e) => FinalizarEdicao(txtEdicao);

            // Adicionar à lista e ao painel
            textBoxesEdicao.Add(txtEdicao);
            pnl_disponibilidade.Controls.Add(txtEdicao);
            label.Visible = false;
            txtEdicao.Focus();
            txtEdicao.SelectAll();
        }

        /// <summary>
        /// Finaliza a edição e salva as alterações
        /// </summary>
        private void FinalizarEdicao(TextBox txtEdicao)
        {
            if (txtEdicao.Tag == null) return;

            dynamic tagInfo = txtEdicao.Tag;
            Label label = tagInfo.Label;
            string campo = tagInfo.Campo;
            DataRow row = tagInfo.Row;

            // Atualizar o valor na DataRow
            try
            {
                switch (campo)
                {
                    case "DIA_SEMANA":
                        row["DIA_SEMANA"] = txtEdicao.Text;
                        break;
                    case "HORARIO":
                        // Para horário, vamos manter o formato original por enquanto
                        // Seria necessário implementar validação de horário aqui
                        break;
                }

                // Atualizar o label
                label.Text = txtEdicao.Text;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao atualizar campo: {ex.Message}", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            // Remover TextBox e mostrar label
            pnl_disponibilidade.Controls.Remove(txtEdicao);
            label.Visible = true;
            textBoxesEdicao.Remove(txtEdicao);
            txtEdicao.Dispose();
        }

        /// <summary>
        /// Cancela a edição sem salvar
        /// </summary>
        private void CancelarEdicao(TextBox txtEdicao)
        {
            if (txtEdicao.Tag == null) return;

            dynamic tagInfo = txtEdicao.Tag;
            Label label = tagInfo.Label;

            // Remover TextBox e mostrar label sem alterar dados
            pnl_disponibilidade.Controls.Remove(txtEdicao);
            label.Visible = true;
            textBoxesEdicao.Remove(txtEdicao);
            txtEdicao.Dispose();
        }

        private void btn_salvar_disponibilidade_disponibilidade_Click(object sender, EventArgs e)
        {
        }

        private void btn_editar_disponibilidade_disponibilidade_Click(object sender, EventArgs e)
        {
        }


        private void btn_excluir_disponibilidade_Click(object sender, EventArgs e)
        {

        }

        private void pnl_header_Paint(object sender, PaintEventArgs e)
        {

        }
    }
}
