﻿namespace ProjetoIntegrador.<PERSON>_Professor
{
    partial class C_Professor
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(C_Professor));
            pnl_header = new Panel();
            btn_voltar_professor = new Button();
            btn_minimizar_professor = new Button();
            btn_maximizar_professor = new Button();
            lbl_titulo = new Label();
            btn_fechar_professor = new Button();
            btn_inserir_professor = new Button();
            lbl_inserirprofessor = new Label();
            txt_RA_professor = new TextBox();
            txt_nomeprofessor_professor = new TextBox();
            dtp_datadenascimentoprofessor_professor = new DateTimePicker();
            cb_nomedisciplina_professor = new ComboBox();
            label3 = new Label();
            label1 = new Label();
            cb_nomecurso_professor = new ComboBox();
            label2 = new Label();
            pnl_header.SuspendLayout();
            SuspendLayout();
            // 
            // pnl_header
            // 
            pnl_header.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            pnl_header.BackColor = Color.MidnightBlue;
            pnl_header.Controls.Add(btn_voltar_professor);
            pnl_header.Controls.Add(btn_minimizar_professor);
            pnl_header.Controls.Add(btn_maximizar_professor);
            pnl_header.Controls.Add(lbl_titulo);
            pnl_header.Controls.Add(btn_fechar_professor);
            pnl_header.Location = new Point(0, 0);
            pnl_header.Name = "pnl_header";
            pnl_header.Size = new Size(1280, 63);
            pnl_header.TabIndex = 27;
            // 
            // btn_voltar_professor
            // 
            btn_voltar_professor.BackColor = Color.MidnightBlue;
            btn_voltar_professor.BackgroundImage = (Image)resources.GetObject("btn_voltar_professor.BackgroundImage");
            btn_voltar_professor.BackgroundImageLayout = ImageLayout.Zoom;
            btn_voltar_professor.FlatAppearance.BorderSize = 0;
            btn_voltar_professor.FlatStyle = FlatStyle.Flat;
            btn_voltar_professor.Location = new Point(35, 18);
            btn_voltar_professor.Name = "btn_voltar_professor";
            btn_voltar_professor.Size = new Size(30, 21);
            btn_voltar_professor.TabIndex = 23;
            btn_voltar_professor.UseVisualStyleBackColor = false;
            btn_voltar_professor.Click += btn_voltar_professor_Click;
            // 
            // btn_minimizar_professor
            // 
            btn_minimizar_professor.BackgroundImage = (Image)resources.GetObject("btn_minimizar_professor.BackgroundImage");
            btn_minimizar_professor.BackgroundImageLayout = ImageLayout.Stretch;
            btn_minimizar_professor.FlatAppearance.BorderSize = 0;
            btn_minimizar_professor.FlatStyle = FlatStyle.Flat;
            btn_minimizar_professor.Location = new Point(1173, 22);
            btn_minimizar_professor.Name = "btn_minimizar_professor";
            btn_minimizar_professor.Size = new Size(21, 19);
            btn_minimizar_professor.TabIndex = 22;
            btn_minimizar_professor.UseVisualStyleBackColor = true;
            btn_minimizar_professor.Click += btn_minimizar_professor_Click;
            // 
            // btn_maximizar_professor
            // 
            btn_maximizar_professor.BackgroundImage = (Image)resources.GetObject("btn_maximizar_professor.BackgroundImage");
            btn_maximizar_professor.BackgroundImageLayout = ImageLayout.Zoom;
            btn_maximizar_professor.FlatAppearance.BorderSize = 0;
            btn_maximizar_professor.FlatStyle = FlatStyle.Flat;
            btn_maximizar_professor.Location = new Point(1209, 18);
            btn_maximizar_professor.Name = "btn_maximizar_professor";
            btn_maximizar_professor.Size = new Size(19, 23);
            btn_maximizar_professor.TabIndex = 21;
            btn_maximizar_professor.UseVisualStyleBackColor = true;
            btn_maximizar_professor.Click += btn_maximizar_professor_Click;
            // 
            // lbl_titulo
            // 
            lbl_titulo.AutoSize = true;
            lbl_titulo.Font = new Font("Segoe UI", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_titulo.ForeColor = SystemColors.ButtonHighlight;
            lbl_titulo.Location = new Point(632, 18);
            lbl_titulo.Name = "lbl_titulo";
            lbl_titulo.Size = new Size(62, 25);
            lbl_titulo.TabIndex = 1;
            lbl_titulo.Text = "SEHD";
            // 
            // btn_fechar_professor
            // 
            btn_fechar_professor.BackColor = Color.MidnightBlue;
            btn_fechar_professor.BackgroundImage = (Image)resources.GetObject("btn_fechar_professor.BackgroundImage");
            btn_fechar_professor.BackgroundImageLayout = ImageLayout.Zoom;
            btn_fechar_professor.FlatAppearance.BorderSize = 0;
            btn_fechar_professor.FlatStyle = FlatStyle.Flat;
            btn_fechar_professor.Location = new Point(1234, 18);
            btn_fechar_professor.Name = "btn_fechar_professor";
            btn_fechar_professor.Size = new Size(34, 21);
            btn_fechar_professor.TabIndex = 20;
            btn_fechar_professor.UseVisualStyleBackColor = false;
            btn_fechar_professor.Click += btn_fechar_professor_Click;
            // 
            // btn_inserir_professor
            // 
            btn_inserir_professor.BackColor = Color.Transparent;
            btn_inserir_professor.BackgroundImage = (Image)resources.GetObject("btn_inserir_professor.BackgroundImage");
            btn_inserir_professor.BackgroundImageLayout = ImageLayout.Zoom;
            btn_inserir_professor.FlatAppearance.BorderSize = 0;
            btn_inserir_professor.FlatStyle = FlatStyle.Flat;
            btn_inserir_professor.ForeColor = SystemColors.ControlLightLight;
            btn_inserir_professor.Location = new Point(599, 517);
            btn_inserir_professor.Name = "btn_inserir_professor";
            btn_inserir_professor.Size = new Size(125, 33);
            btn_inserir_professor.TabIndex = 44;
            btn_inserir_professor.Text = "Inserir";
            btn_inserir_professor.UseVisualStyleBackColor = false;
            btn_inserir_professor.Click += btn_inserir_Click;
            // 
            // lbl_inserirprofessor
            // 
            lbl_inserirprofessor.AutoSize = true;
            lbl_inserirprofessor.Font = new Font("Segoe UI", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_inserirprofessor.ForeColor = Color.MidnightBlue;
            lbl_inserirprofessor.Location = new Point(579, 166);
            lbl_inserirprofessor.Name = "lbl_inserirprofessor";
            lbl_inserirprofessor.Size = new Size(159, 25);
            lbl_inserirprofessor.TabIndex = 43;
            lbl_inserirprofessor.Text = "Inserir professor";
            // 
            // txt_RA_professor
            // 
            txt_RA_professor.Location = new Point(465, 276);
            txt_RA_professor.Name = "txt_RA_professor";
            txt_RA_professor.PlaceholderText = "RA";
            txt_RA_professor.Size = new Size(404, 23);
            txt_RA_professor.TabIndex = 41;
            // 
            // txt_nomeprofessor_professor
            // 
            txt_nomeprofessor_professor.Location = new Point(465, 219);
            txt_nomeprofessor_professor.Name = "txt_nomeprofessor_professor";
            txt_nomeprofessor_professor.PlaceholderText = "Nome do professor";
            txt_nomeprofessor_professor.Size = new Size(404, 23);
            txt_nomeprofessor_professor.TabIndex = 40;
            // 
            // dtp_datadenascimentoprofessor_professor
            // 
            dtp_datadenascimentoprofessor_professor.Location = new Point(465, 343);
            dtp_datadenascimentoprofessor_professor.Name = "dtp_datadenascimentoprofessor_professor";
            dtp_datadenascimentoprofessor_professor.Size = new Size(404, 23);
            dtp_datadenascimentoprofessor_professor.TabIndex = 47;
            dtp_datadenascimentoprofessor_professor.Value = new DateTime(2025, 5, 20, 20, 30, 56, 0);
            // 
            // cb_nomedisciplina_professor
            // 
            cb_nomedisciplina_professor.FormattingEnabled = true;
            cb_nomedisciplina_professor.Location = new Point(465, 409);
            cb_nomedisciplina_professor.Name = "cb_nomedisciplina_professor";
            cb_nomedisciplina_professor.Size = new Size(404, 23);
            cb_nomedisciplina_professor.TabIndex = 48;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Font = new Font("Segoe UI Semibold", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            label3.ForeColor = Color.DimGray;
            label3.Location = new Point(465, 389);
            label3.Name = "label3";
            label3.Size = new Size(123, 17);
            label3.TabIndex = 49;
            label3.Text = "Nome da disciplina";
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Font = new Font("Segoe UI Semibold", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            label1.ForeColor = Color.DimGray;
            label1.Location = new Point(465, 450);
            label1.Name = "label1";
            label1.Size = new Size(102, 17);
            label1.TabIndex = 50;
            label1.Text = "Nome do curso";
            // 
            // cb_nomecurso_professor
            // 
            cb_nomecurso_professor.FormattingEnabled = true;
            cb_nomecurso_professor.Location = new Point(465, 470);
            cb_nomecurso_professor.Name = "cb_nomecurso_professor";
            cb_nomecurso_professor.Size = new Size(404, 23);
            cb_nomecurso_professor.TabIndex = 51;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Font = new Font("Segoe UI Semibold", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            label2.ForeColor = Color.DimGray;
            label2.Location = new Point(465, 323);
            label2.Name = "label2";
            label2.Size = new Size(129, 17);
            label2.TabIndex = 52;
            label2.Text = "Data de nascimento";
            //
            // C_Professor
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1280, 702);
            Controls.Add(label2);
            Controls.Add(cb_nomecurso_professor);
            Controls.Add(label1);
            Controls.Add(label3);
            Controls.Add(cb_nomedisciplina_professor);
            Controls.Add(dtp_datadenascimentoprofessor_professor);
            Controls.Add(btn_inserir_professor);
            Controls.Add(lbl_inserirprofessor);
            Controls.Add(txt_RA_professor);
            Controls.Add(txt_nomeprofessor_professor);
            Controls.Add(pnl_header);
            FormBorderStyle = FormBorderStyle.None;
            Name = "C_Professor";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "C_IProfessor";
            pnl_header.ResumeLayout(false);
            pnl_header.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion
        private Panel pnl_header;
        private Button btn_voltar_professor;
        private Button btn_minimizar_professor;
        private Button btn_maximizar_professor;
        private Label lbl_titulo;
        private Button btn_fechar_professor;
        private Button btn_inserir_professor;
        private Label lbl_inserirprofessor;
        private TextBox txt_RA_professor;
        private TextBox txt_nomeprofessor_professor;
        private DateTimePicker dtp_datadenascimentoprofessor_professor;
        private ComboBox cb_nomedisciplina_professor;
        private Label label3;
        private Label label1;
        private ComboBox cb_nomecurso_professor;
        private Label label2;
    }
}