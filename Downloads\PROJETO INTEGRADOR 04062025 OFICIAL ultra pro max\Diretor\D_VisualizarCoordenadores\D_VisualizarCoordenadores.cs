using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using ProjetoIntegrador.Classes;

namespace ProjetoIntegrador.Diretor.D_VisualizarCoordenadores
{
    public partial class D_VisualizarCoordenadores : Form
    {
        private comandosbanco cmdBanco = new comandosbanco();
        private DataTable dadosCoordenadores;
        private int paginaAtual = 0;
        private int registrosPorPagina = 9;
        private int totalPaginas = 0;
        private bool modoEdicao = false;
        private List<Label> labelsNome = new List<Label>();
        private List<Label> labelsCurso = new List<Label>();
        private List<TextBox> textBoxesEdicao = new List<TextBox>();
        private List<CheckBox> checkBoxes = new List<CheckBox>();
        private HashSet<int> itensSelecionadosGlobal = new HashSet<int>(); // Para manter seleções entre páginas

        public D_VisualizarCoordenadores()
        {
            InitializeComponent();
            InicializarComponentes();
            CarregarCoordenadores();
        }

        private void InicializarComponentes()
        {
            // Inicializar lista de checkboxes
            checkBoxes.AddRange(new CheckBox[] {
                cb_linha1_visualizardisciplinas,
                checkBox1,
                checkBox2,
                checkBox3,
                checkBox4,
                checkBox5,
                checkBox6,
                checkBox7,
                checkBox8
            });

            // Criar labels para exibir dados
            CriarLabelsParaDados();

            // Configurar botão salvar como invisível inicialmente
            btn_salvar_visualizarcoordenador.Visible = false;
        }

        private void CriarLabelsParaDados()
        {
            for (int i = 0; i < registrosPorPagina; i++)
            {
                // Labels para Nome
                Label lblNome = new Label();
                lblNome.AutoSize = false;
                lblNome.Size = new Size(400, 40);
                lblNome.TextAlign = ContentAlignment.MiddleLeft;
                lblNome.Padding = new Padding(25, 0, 0, 0);
                lblNome.BackColor = Color.Transparent;
                lblNome.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                lblNome.DoubleClick += (s, e) => IniciarEdicao(s as Label, "NOME_COORDENADOR");
                pnl_grade1.Controls.Add(lblNome, 0, i + 1);
                labelsNome.Add(lblNome);

                // Labels para Curso
                Label lblCurso = new Label();
                lblCurso.AutoSize = false;
                lblCurso.Size = new Size(400, 40);
                lblCurso.TextAlign = ContentAlignment.MiddleLeft;
                lblCurso.Padding = new Padding(25, 0, 0, 0);
                lblCurso.BackColor = Color.Transparent;
                lblCurso.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                lblCurso.DoubleClick += (s, e) => IniciarEdicao(s as Label, "NOME_CURSO");
                pnl_grade1.Controls.Add(lblCurso, 1, i + 1);
                labelsCurso.Add(lblCurso);
            }
        }

        private void CarregarCoordenadores()
        {
            try
            {
                dadosCoordenadores = cmdBanco.ConsultarCoordenadores();
                totalPaginas = (int)Math.Ceiling((double)dadosCoordenadores.Rows.Count / registrosPorPagina);
                if (totalPaginas == 0) totalPaginas = 1;

                ExibirDadosPagina();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar coordenadores: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExibirDadosPagina()
        {
            // Suspender layout para melhorar performance
            pnl_grade1.SuspendLayout();

            try
            {
                // Salvar seleções da página atual antes de trocar
                SalvarSelecoesPaginaAtual();

                // Limpar e ocultar todos os controles de uma vez
                for (int i = 0; i < registrosPorPagina; i++)
                {
                    checkBoxes[i].Visible = false;
                    checkBoxes[i].Checked = false;
                    checkBoxes[i].Tag = null;

                    labelsNome[i].Text = "";
                    labelsNome[i].Tag = null;

                    labelsCurso[i].Text = "";
                    labelsCurso[i].Tag = null;
                }

                if (dadosCoordenadores == null || dadosCoordenadores.Rows.Count == 0)
                    return;

                int inicioIndice = paginaAtual * registrosPorPagina;
                int fimIndice = Math.Min(inicioIndice + registrosPorPagina, dadosCoordenadores.Rows.Count);

                // Preencher apenas os dados necessários
                for (int i = inicioIndice; i < fimIndice; i++)
                {
                    int indiceLabel = i - inicioIndice;
                    DataRow row = dadosCoordenadores.Rows[i];

                    // Configurar checkbox
                    checkBoxes[indiceLabel].Visible = true;
                    checkBoxes[indiceLabel].Tag = row;

                    // Restaurar seleção se estava selecionado globalmente
                    int idCoordenador = Convert.ToInt32(row["ID_COORDENADOR"]);
                    checkBoxes[indiceLabel].Checked = itensSelecionadosGlobal.Contains(idCoordenador);

                    // Preencher dados nos labels de forma otimizada
                    labelsNome[indiceLabel].Text = row["NOME_COORDENADOR"].ToString();
                    labelsNome[indiceLabel].Tag = row;

                    labelsCurso[indiceLabel].Text = row["NOME_CURSO"]?.ToString() ?? "Curso não vinculado";
                    labelsCurso[indiceLabel].Tag = row;
                }

                AtualizarControlePaginacao();
            }
            finally
            {
                // Retomar layout
                pnl_grade1.ResumeLayout(true);
            }
        }

        private void SalvarSelecoesPaginaAtual()
        {
            for (int i = 0; i < registrosPorPagina; i++)
            {
                if (checkBoxes[i].Visible && checkBoxes[i].Tag != null)
                {
                    DataRow row = (DataRow)checkBoxes[i].Tag;
                    int idCoordenador = Convert.ToInt32(row["ID_COORDENADOR"]);

                    if (checkBoxes[i].Checked)
                    {
                        itensSelecionadosGlobal.Add(idCoordenador);
                    }
                    else
                    {
                        itensSelecionadosGlobal.Remove(idCoordenador);
                    }
                }
            }
        }

        /// <summary>
        /// Obtém todos os itens selecionados de todas as páginas
        /// </summary>
        private List<DataRow> ObterTodosItensSelecionados()
        {
            // Salvar seleções da página atual
            SalvarSelecoesPaginaAtual();

            // Buscar todas as linhas selecionadas no DataTable
            var itensSelecionados = new List<DataRow>();

            foreach (DataRow row in dadosCoordenadores.Rows)
            {
                int idCoordenador = Convert.ToInt32(row["ID_COORDENADOR"]);
                if (itensSelecionadosGlobal.Contains(idCoordenador))
                {
                    itensSelecionados.Add(row);
                }
            }

            return itensSelecionados;
        }

        private void AtualizarControlePaginacao()
        {
            lbl_paginacao.Text = $"Página {paginaAtual + 1} de {totalPaginas}";
            btn_anterior_paginacao.Enabled = paginaAtual > 0;
            btn_proximo_paginacao.Enabled = paginaAtual < totalPaginas - 1;
        }

        private void btn_anterior_paginacao_Click(object sender, EventArgs e)
        {
            if (paginaAtual > 0)
            {
                paginaAtual--;
                ExibirDadosPagina();
            }
        }

        private void btn_proximo_paginacao_Click(object sender, EventArgs e)
        {
            if (paginaAtual < totalPaginas - 1)
            {
                paginaAtual++;
                ExibirDadosPagina();
            }
        }

        private void btn_fechar_visualizarcoordenador_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        private void btn_minimizar_visualizarcoordenador_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_visualizarcoordenador_Click(object sender, EventArgs e)
        {
            ToggleMaximizeWindow();
        }

        private void ToggleMaximizeWindow()
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                this.WindowState = FormWindowState.Normal;
                this.StartPosition = FormStartPosition.CenterScreen;
                this.CenterToScreen();
            }
            else
            {
                this.WindowState = FormWindowState.Maximized;
            }
        }

        private void IniciarEdicao(Label label, string campo)
        {
            if (!modoEdicao)
            {
                MessageBox.Show("Ative o modo de edição primeiro clicando no botão 'Editar'.", "Modo Edição", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (label.Tag == null) return;

            DataRow row = (DataRow)label.Tag;

            // Criar TextBox para edição
            TextBox txtEdicao = new TextBox();
            txtEdicao.Text = label.Text;
            txtEdicao.Size = label.Size;
            txtEdicao.Location = label.Location;
            txtEdicao.Font = label.Font;
            txtEdicao.Tag = new { Label = label, Row = row, Campo = campo };

            // Eventos do TextBox
            txtEdicao.KeyDown += (s, e) =>
            {
                if (e.KeyCode == Keys.Enter)
                {
                    FinalizarEdicao(txtEdicao);
                }
                else if (e.KeyCode == Keys.Escape)
                {
                    CancelarEdicao(txtEdicao);
                }
            };

            txtEdicao.LostFocus += (s, e) => FinalizarEdicao(txtEdicao);

            // Adicionar TextBox ao painel na mesma posição do label
            TableLayoutPanelCellPosition posicao = pnl_grade1.GetCellPosition(label);
            pnl_grade1.Controls.Add(txtEdicao, posicao.Column, posicao.Row);

            // Ocultar label e focar no TextBox
            label.Visible = false;
            txtEdicao.Focus();
            txtEdicao.SelectAll();

            textBoxesEdicao.Add(txtEdicao);
        }

        private void btn_editar_visualizarcoordenador_Click(object sender, EventArgs e)
        {
            modoEdicao = true;
            btn_salvar_visualizarcoordenador.Visible = true;
            MessageBox.Show("Modo de edição ativado. Dê duplo clique nos campos para editá-los.", "Modo Edição", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btn_salvar_visualizarcoordenador_Click(object sender, EventArgs e)
        {
            try
            {
                // Finalizar todas as edições pendentes
                var textBoxesParaFinalizar = new List<TextBox>(textBoxesEdicao);
                foreach (var txtBox in textBoxesParaFinalizar)
                {
                    FinalizarEdicao(txtBox);
                }

                // Salvar alterações no banco de dados
                foreach (DataRow row in dadosCoordenadores.Rows)
                {
                    if (row.RowState == DataRowState.Modified)
                    {
                        variaveisbanco modelo = new variaveisbanco
                        {
                            nomecoordenador = row["NOME_COORDENADOR"].ToString(),
                            nomecurso = row["NOME_CURSO"].ToString()
                        };

                        int idCoordenador = Convert.ToInt32(row["ID_COORDENADOR"]);
                        cmdBanco.AtualizarCoordenador(modelo, idCoordenador);
                    }
                }

                MessageBox.Show("Alterações salvas com sucesso!", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Sair do modo de edição
                modoEdicao = false;
                btn_salvar_visualizarcoordenador.Visible = false;

                // Recarregar dados
                CarregarCoordenadores();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao salvar alterações: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            var itensSelecionados = ObterTodosItensSelecionados();

            if (itensSelecionados.Count == 0)
            {
                MessageBox.Show("Selecione pelo menos um coordenador para excluir.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            DialogResult resultado = MessageBox.Show(
                $"Deseja realmente excluir {itensSelecionados.Count} coordenador(es) selecionado(s)?",
                "Confirmação de Exclusão",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (resultado == DialogResult.Yes)
            {
                try
                {
                    foreach (DataRow row in itensSelecionados)
                    {
                        int idCoordenador = Convert.ToInt32(row["ID_COORDENADOR"]);
                        cmdBanco.DeletarCoordenador(idCoordenador);
                    }

                    MessageBox.Show($"{itensSelecionados.Count} coordenador(es) excluído(s) com sucesso!", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Limpar seleções e recarregar dados
                    itensSelecionadosGlobal.Clear();
                    CarregarCoordenadores();
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Erro ao excluir coordenador(es): " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void FinalizarEdicao(TextBox txtEdicao)
        {
            try
            {
                var tagInfo = txtEdicao.Tag as dynamic;
                Label label = tagInfo.Label;
                DataRow row = tagInfo.Row;
                string campo = tagInfo.Campo;

                string novoValor = txtEdicao.Text.Trim();

                // Validar se o campo não está vazio
                if (string.IsNullOrWhiteSpace(novoValor))
                {
                    MessageBox.Show("O campo não pode estar vazio.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEdicao.Focus();
                    return;
                }

                // Atualizar o DataRow
                row[campo] = novoValor;

                // Atualizar o label
                label.Text = novoValor;
                label.Visible = true;

                // Remover TextBox
                pnl_grade1.Controls.Remove(txtEdicao);
                textBoxesEdicao.Remove(txtEdicao);
                txtEdicao.Dispose();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao finalizar edição: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelarEdicao(TextBox txtEdicao)
        {
            try
            {
                var tagInfo = txtEdicao.Tag as dynamic;
                Label label = tagInfo.Label;

                // Restaurar label
                label.Visible = true;

                // Remover TextBox
                pnl_grade1.Controls.Remove(txtEdicao);
                textBoxesEdicao.Remove(txtEdicao);
                txtEdicao.Dispose();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao cancelar edição: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void lbl_nomecurso_Click(object sender, EventArgs e)
        {

        }
    }
}
