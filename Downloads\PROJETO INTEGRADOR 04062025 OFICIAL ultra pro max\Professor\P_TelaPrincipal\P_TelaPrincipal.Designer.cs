﻿namespace ProjetoIntegrador.Professor.P_TelaPrincipal
{
    partial class P_TelaPrincipal
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(P_TelaPrincipal));
            pnl_header = new Panel();
            btn_minimizar_professor = new Button();
            btn_maximizar_professor = new Button();
            lbl_titulo = new Label();
            btn_fechar_professor = new Button();
            pnl_lado_C1 = new Panel();
            btn_disponibilidade_telaprincipal = new Button();
            btn_visualizardisponibilidade_telaprincipal = new Button();
            btn_conferirgrade_telaprincipal = new Button();
            lbl_bemvindoC = new Label();
            pnl_header.SuspendLayout();
            pnl_lado_C1.SuspendLayout();
            SuspendLayout();
            // 
            // pnl_header
            // 
            pnl_header.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            pnl_header.BackColor = Color.MidnightBlue;
            pnl_header.Controls.Add(btn_minimizar_professor);
            pnl_header.Controls.Add(btn_maximizar_professor);
            pnl_header.Controls.Add(lbl_titulo);
            pnl_header.Controls.Add(btn_fechar_professor);
            pnl_header.Location = new Point(0, 0);
            pnl_header.Name = "pnl_header";
            pnl_header.Size = new Size(1280, 63);
            pnl_header.TabIndex = 10;
            // 
            // btn_minimizar_professor
            // 
            btn_minimizar_professor.BackgroundImage = (Image)resources.GetObject("btn_minimizar_professor.BackgroundImage");
            btn_minimizar_professor.BackgroundImageLayout = ImageLayout.Stretch;
            btn_minimizar_professor.FlatAppearance.BorderSize = 0;
            btn_minimizar_professor.FlatStyle = FlatStyle.Flat;
            btn_minimizar_professor.Location = new Point(1173, 22);
            btn_minimizar_professor.Name = "btn_minimizar_professor";
            btn_minimizar_professor.Size = new Size(21, 19);
            btn_minimizar_professor.TabIndex = 22;
            btn_minimizar_professor.UseVisualStyleBackColor = true;
            btn_minimizar_professor.Click += btn_minimizar_professor_Click;
            // 
            // btn_maximizar_professor
            // 
            btn_maximizar_professor.BackgroundImage = (Image)resources.GetObject("btn_maximizar_professor.BackgroundImage");
            btn_maximizar_professor.BackgroundImageLayout = ImageLayout.Zoom;
            btn_maximizar_professor.FlatAppearance.BorderSize = 0;
            btn_maximizar_professor.FlatStyle = FlatStyle.Flat;
            btn_maximizar_professor.Location = new Point(1209, 18);
            btn_maximizar_professor.Name = "btn_maximizar_professor";
            btn_maximizar_professor.Size = new Size(19, 23);
            btn_maximizar_professor.TabIndex = 21;
            btn_maximizar_professor.UseVisualStyleBackColor = true;
            btn_maximizar_professor.Click += btn_maximizar_professor_Click;
            // 
            // lbl_titulo
            // 
            lbl_titulo.AutoSize = true;
            lbl_titulo.Font = new Font("Segoe UI", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_titulo.ForeColor = SystemColors.ButtonHighlight;
            lbl_titulo.Location = new Point(632, 18);
            lbl_titulo.Name = "lbl_titulo";
            lbl_titulo.Size = new Size(62, 25);
            lbl_titulo.TabIndex = 1;
            lbl_titulo.Text = "SEHD";
            // 
            // btn_fechar_professor
            // 
            btn_fechar_professor.BackColor = Color.MidnightBlue;
            btn_fechar_professor.BackgroundImage = (Image)resources.GetObject("btn_fechar_professor.BackgroundImage");
            btn_fechar_professor.BackgroundImageLayout = ImageLayout.Zoom;
            btn_fechar_professor.FlatAppearance.BorderSize = 0;
            btn_fechar_professor.FlatStyle = FlatStyle.Flat;
            btn_fechar_professor.Location = new Point(1234, 18);
            btn_fechar_professor.Name = "btn_fechar_professor";
            btn_fechar_professor.Size = new Size(34, 21);
            btn_fechar_professor.TabIndex = 20;
            btn_fechar_professor.UseVisualStyleBackColor = false;
            btn_fechar_professor.Click += btn_fechar_professor_Click;
            // 
            // pnl_lado_C1
            // 
            pnl_lado_C1.BackColor = Color.DarkGray;
            pnl_lado_C1.BorderStyle = BorderStyle.FixedSingle;
            pnl_lado_C1.Controls.Add(btn_disponibilidade_telaprincipal);
            pnl_lado_C1.Controls.Add(btn_visualizardisponibilidade_telaprincipal);
            pnl_lado_C1.Controls.Add(btn_conferirgrade_telaprincipal);
            pnl_lado_C1.ForeColor = SystemColors.ControlLightLight;
            pnl_lado_C1.Location = new Point(35, 247);
            pnl_lado_C1.Name = "pnl_lado_C1";
            pnl_lado_C1.Size = new Size(212, 242);
            pnl_lado_C1.TabIndex = 28;
            // 
            // btn_disponibilidade_telaprincipal
            // 
            btn_disponibilidade_telaprincipal.BackColor = Color.Transparent;
            btn_disponibilidade_telaprincipal.BackgroundImage = (Image)resources.GetObject("btn_disponibilidade_telaprincipal.BackgroundImage");
            btn_disponibilidade_telaprincipal.BackgroundImageLayout = ImageLayout.Zoom;
            btn_disponibilidade_telaprincipal.FlatAppearance.BorderSize = 0;
            btn_disponibilidade_telaprincipal.FlatStyle = FlatStyle.Flat;
            btn_disponibilidade_telaprincipal.ForeColor = SystemColors.ControlLightLight;
            btn_disponibilidade_telaprincipal.Location = new Point(22, 15);
            btn_disponibilidade_telaprincipal.Name = "btn_disponibilidade_telaprincipal";
            btn_disponibilidade_telaprincipal.Size = new Size(159, 66);
            btn_disponibilidade_telaprincipal.TabIndex = 4;
            btn_disponibilidade_telaprincipal.Text = "Disponibilidade";
            btn_disponibilidade_telaprincipal.UseVisualStyleBackColor = false;
            btn_disponibilidade_telaprincipal.Click += btn_disponibilidade_Click;
            // 
            // btn_visualizardisponibilidade_telaprincipal
            // 
            btn_visualizardisponibilidade_telaprincipal.BackColor = Color.Transparent;
            btn_visualizardisponibilidade_telaprincipal.BackgroundImage = (Image)resources.GetObject("btn_visualizardisponibilidade_telaprincipal.BackgroundImage");
            btn_visualizardisponibilidade_telaprincipal.BackgroundImageLayout = ImageLayout.Zoom;
            btn_visualizardisponibilidade_telaprincipal.FlatAppearance.BorderSize = 0;
            btn_visualizardisponibilidade_telaprincipal.FlatStyle = FlatStyle.Flat;
            btn_visualizardisponibilidade_telaprincipal.ForeColor = SystemColors.ControlLightLight;
            btn_visualizardisponibilidade_telaprincipal.Location = new Point(22, 87);
            btn_visualizardisponibilidade_telaprincipal.Name = "btn_visualizardisponibilidade_telaprincipal";
            btn_visualizardisponibilidade_telaprincipal.Size = new Size(159, 66);
            btn_visualizardisponibilidade_telaprincipal.TabIndex = 16;
            btn_visualizardisponibilidade_telaprincipal.Text = "Visualizar disponibilidade";
            btn_visualizardisponibilidade_telaprincipal.UseVisualStyleBackColor = false;
            btn_visualizardisponibilidade_telaprincipal.Click += btn_visualizardisponibilidade_Click;
            // 
            // btn_conferirgrade_telaprincipal
            // 
            btn_conferirgrade_telaprincipal.BackColor = Color.Transparent;
            btn_conferirgrade_telaprincipal.BackgroundImage = (Image)resources.GetObject("btn_conferirgrade_telaprincipal.BackgroundImage");
            btn_conferirgrade_telaprincipal.BackgroundImageLayout = ImageLayout.Zoom;
            btn_conferirgrade_telaprincipal.FlatAppearance.BorderSize = 0;
            btn_conferirgrade_telaprincipal.FlatStyle = FlatStyle.Flat;
            btn_conferirgrade_telaprincipal.ForeColor = SystemColors.ControlLightLight;
            btn_conferirgrade_telaprincipal.Location = new Point(22, 159);
            btn_conferirgrade_telaprincipal.Name = "btn_conferirgrade_telaprincipal";
            btn_conferirgrade_telaprincipal.Size = new Size(159, 66);
            btn_conferirgrade_telaprincipal.TabIndex = 6;
            btn_conferirgrade_telaprincipal.Text = "Conferir Grade";
            btn_conferirgrade_telaprincipal.UseVisualStyleBackColor = false;
            btn_conferirgrade_telaprincipal.Click += btn_conferirgrade_Click;
            // 
            // lbl_bemvindoC
            // 
            lbl_bemvindoC.AutoSize = true;
            lbl_bemvindoC.Font = new Font("Segoe UI", 20.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_bemvindoC.ForeColor = Color.MidnightBlue;
            lbl_bemvindoC.Location = new Point(632, 349);
            lbl_bemvindoC.Name = "lbl_bemvindoC";
            lbl_bemvindoC.Size = new Size(295, 37);
            lbl_bemvindoC.TabIndex = 29;
            lbl_bemvindoC.Text = "Bem vindo, Professor.";
            // 
            // P_TelaPrincipal
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1280, 702);
            Controls.Add(lbl_bemvindoC);
            Controls.Add(pnl_lado_C1);
            Controls.Add(pnl_header);
            FormBorderStyle = FormBorderStyle.None;
            Margin = new Padding(3, 2, 3, 2);
            Name = "P_TelaPrincipal";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "P_TelaPrincipal";
            Load += P_TelaPrincipal_Load;
            pnl_header.ResumeLayout(false);
            pnl_header.PerformLayout();
            pnl_lado_C1.ResumeLayout(false);
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Panel pnl_header;
        private Button btn_minimizar_professor;
        private Button btn_maximizar_professor;
        private Label lbl_titulo;
        private Button btn_fechar_professor;
        private Panel pnl_lado_C1;
        private Button btn_disponibilidade_telaprincipal;
        private Button btn_visualizardisponibilidade_telaprincipal;
        private Button btn_conferirgrade_telaprincipal;
        private Label lbl_bemvindoC;
    }
}