using ProjetoIntegrador.Classes;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ProjetoIntegrador.Diretor
{
    public partial class D_Coordenador : Form
    {
        public D_Coordenador()
        {
            InitializeComponent();
            CarregarDadosComboBoxes();

            // Adicionar evento para validar RA em tempo real
            txt_ra_coordenador.KeyPress += txt_ra_coordenador_KeyPress;
        }

        // Método para carregar dados nas ComboBoxes
        private string GerarMD5(string input)
        {
            using (MD5 md5 = MD5.Create())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashBytes = md5.ComputeHash(inputBytes);
                StringBuilder sb = new StringBuilder();
                foreach (byte b in hashBytes)
                    sb.Append(b.ToString("x2"));
                return sb.ToString();
            }
        }

        // Método para carregar dados nas ComboBoxes
        private void CarregarDadosComboBoxes()
        {
            try
            {
                comandosbanco cmdBanco = new comandosbanco();
                // Carregar cursos
                DataTable cursos = cmdBanco.ConsultarCursos();
                cb_selecionarcurso_coordenador.Items.Clear();
                cb_selecionarcurso_coordenador.Items.Add("Selecione um curso");
                foreach (DataRow row in cursos.Rows)
                {
                    cb_selecionarcurso_coordenador.Items.Add(row["NOME_CURSO"].ToString());
                }
                cb_selecionarcurso_coordenador.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar dados: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btn_maximizar_coordenador_Click(object sender, EventArgs e)
        {
            ToggleMaximizeWindow();
        }

        /// <summary>
        /// Alterna entre maximizar e restaurar a janela, centralizando quando restaura
        /// </summary>
        private void ToggleMaximizeWindow()
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                // Se está maximizada, restaura ao tamanho normal e centraliza
                this.WindowState = FormWindowState.Normal;
                this.StartPosition = FormStartPosition.CenterScreen;
                this.CenterToScreen();
            }
            else
            {
                // Se não está maximizada, maximiza
                this.WindowState = FormWindowState.Maximized;
            }
        }

        private void btn_minimizar_coordenador_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_fechar_coordenador_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question
            );

            if (resultado == DialogResult.Yes)
            {
                this.Close();
            }
            // Se for "No", nada acontece e o formulário continua aberto.
        }



        // Método para limpar os campos do formulário
        private void LimparCampos()
        {
            try
            {
                // Limpar campos de texto
                txt_nomecoordenador_coordenador.Clear();
                txt_ra_coordenador.Clear();

                // Resetar data de nascimento para hoje
                dtp_datadenascimento_coordenador.Value = DateTime.Now;

                // Resetar combobox de curso
                cb_selecionarcurso_coordenador.SelectedIndex = 0;

                // Focar no primeiro campo
                txt_nomecoordenador_coordenador.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao limpar campos: {ex.Message}",
                              "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Valida entrada do RA em tempo real - apenas números e máximo 7 dígitos
        /// </summary>
        private void txt_ra_coordenador_KeyPress(object sender, KeyPressEventArgs e)
        {
            // Permitir apenas números, backspace e delete
            if (!char.IsDigit(e.KeyChar) && e.KeyChar != (char)Keys.Back)
            {
                e.Handled = true;
                return;
            }

            // Verificar se já tem 7 dígitos e não é backspace
            TextBox textBox = sender as TextBox;
            if (textBox.Text.Length >= 7 && e.KeyChar != (char)Keys.Back)
            {
                e.Handled = true;
            }
        }

        private void btn_voltar_coordenador_Click(object sender, EventArgs e)
        {
            ProjetoIntegrador.Diretor.D_TelaPrincipal.D_TelaPrincipal telaPrincipal = new ProjetoIntegrador.Diretor.D_TelaPrincipal.D_TelaPrincipal();
            telaPrincipal.Show();
            this.Hide();

        }

        private void btn_inserir_coordenador_Click(object sender, EventArgs e)
        {
            // Validar campos obrigatórios
            if (string.IsNullOrWhiteSpace(txt_nomecoordenador_coordenador.Text))
            {
                MessageBox.Show("O campo 'Nome do Coordenador' é obrigatório.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txt_nomecoordenador_coordenador.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txt_ra_coordenador.Text))
            {
                MessageBox.Show("O campo 'RA' é obrigatório.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txt_ra_coordenador.Focus();
                return;
            }

            if (cb_selecionarcurso_coordenador.SelectedIndex <= 0 ||
                cb_selecionarcurso_coordenador.SelectedItem == null ||
                cb_selecionarcurso_coordenador.SelectedItem.ToString() == "Selecione um curso")
            {
                MessageBox.Show("Por favor, selecione um curso.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cb_selecionarcurso_coordenador.Focus();
                return;
            }

            // Validar se o RA é um número válido e tem exatamente 7 dígitos
            if (!int.TryParse(txt_ra_coordenador.Text, out int ra))
            {
                MessageBox.Show("O RA deve ser um número válido.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txt_ra_coordenador.Focus();
                return;
            }

            // Validar se o RA tem exatamente 7 dígitos
            if (txt_ra_coordenador.Text.Length != 7)
            {
                MessageBox.Show("O RA deve ter exatamente 7 dígitos.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txt_ra_coordenador.Focus();
                return;
            }

            // Validar se a data de nascimento é válida (não pode ser futura)
            DateTime dataNascimento = dtp_datadenascimento_coordenador.Value;
            if (dataNascimento.Date > DateTime.Now.Date)
            {
                MessageBox.Show("A data de nascimento não pode ser uma data futura.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                dtp_datadenascimento_coordenador.Focus();
                return;
            }

            try
            {
                // Converter a data de nascimento para o formato DDMMAAAA
                string dataFormatada = dataNascimento.ToString("ddMMyyyy");

                // Gerar hash MD5 da data de nascimento
                string dataNascimentoMD5 = GerarMD5(dataFormatada);

                // Criar o modelo com os dados do formulário
                variaveisbanco modelo = new variaveisbanco
                {
                    nomecoordenador = txt_nomecoordenador_coordenador.Text.Trim(),
                    racoordenador = ra,
                    datanascimentocoordenador = dataNascimentoMD5, // Usar MD5 como string
                    nomecurso = cb_selecionarcurso_coordenador.SelectedItem.ToString()
                };

                // Instanciar a classe de comandos do banco
                comandosbanco cmdBanco = new comandosbanco();

                // Cadastrar o coordenador
                cmdBanco.CadastroCoordenador(modelo);

                MessageBox.Show("Coordenador cadastrado com sucesso!", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Limpar os campos após o cadastro
                LimparCampos();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao cadastrar coordenador: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
