using System;

namespace ProjetoIntegrador.Classes
{
    /// <summary>
    /// Classe estática para armazenar informações da sessão do usuário logado
    /// </summary>
    public static class SessaoUsuario
    {
        /// <summary>
        /// RA do usuário logado
        /// </summary>
        public static string RA { get; set; } = string.Empty;

        /// <summary>
        /// Tipo do usuário logado (P = Professor, C = Coordenador, D = Diretor)
        /// </summary>
        public static string TipoUsuario { get; set; } = string.Empty;

        /// <summary>
        /// ID do professor (apenas para professores)
        /// </summary>
        public static int? IdProfessor { get; set; } = null;

        /// <summary>
        /// Nome do usuário logado
        /// </summary>
        public static string NomeUsuario { get; set; } = string.Empty;

        /// <summary>
        /// Verifica se há um usuário logado
        /// </summary>
        public static bool UsuarioLogado => !string.IsNullOrEmpty(RA) && !string.IsNullOrEmpty(TipoUsuario);

        /// <summary>
        /// Verifica se o usuário logado é um professor
        /// </summary>
        public static bool EhProfessor => TipoUsuario == "P";

        /// <summary>
        /// Verifica se o usuário logado é um coordenador
        /// </summary>
        public static bool EhCoordenador => TipoUsuario == "C";

        /// <summary>
        /// Verifica se o usuário logado é um diretor
        /// </summary>
        public static bool EhDiretor => TipoUsuario == "D";

        /// <summary>
        /// Limpa todas as informações da sessão (logout)
        /// </summary>
        public static void LimparSessao()
        {
            RA = string.Empty;
            TipoUsuario = string.Empty;
            IdProfessor = null;
            NomeUsuario = string.Empty;
        }

        /// <summary>
        /// Define as informações da sessão do usuário
        /// </summary>
        /// <param name="ra">RA do usuário</param>
        /// <param name="tipoUsuario">Tipo do usuário (P/C/D)</param>
        /// <param name="nomeUsuario">Nome do usuário</param>
        /// <param name="idProfessor">ID do professor (opcional, apenas para professores)</param>
        public static void DefinirSessao(string ra, string tipoUsuario, string nomeUsuario = "", int? idProfessor = null)
        {
            RA = ra ?? string.Empty;
            TipoUsuario = tipoUsuario ?? string.Empty;
            NomeUsuario = nomeUsuario ?? string.Empty;
            IdProfessor = idProfessor;
        }

        /// <summary>
        /// Obtém uma string com informações da sessão para debug
        /// </summary>
        /// <returns>String com informações da sessão</returns>
        public static string ObterInfoSessao()
        {
            return $"RA: {RA}, Tipo: {TipoUsuario}, Nome: {NomeUsuario}, ID Professor: {IdProfessor}";
        }
    }
}
