using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace ProjetoIntegrador.Diretor.D_VisualizarCursos
{
    public partial class D_VisualizarCursos : Form
    {
        // Variáveis importantes para paginação e modo de edição
        private int paginaAtual = 0;
        private int totalPaginas = 0;

        // Flag para saber se está em modo edição
        private bool modoEdicao = false;

        // Coleção de TextBoxes que podem ser editados
        private List<TextBox> textBoxesEdicao = new List<TextBox>();

        // DataTable para armazenar dados dos cursos/coordenadores
        private DataTable dadosCurso = new DataTable();

        // Objeto para acessar o banco de dados (você deve implementar essa classe)
        private CmdBanco cmdBanco = new CmdBanco();

        public D_VisualizarCursos()
        {
            InitializeComponent();

            // Exemplo: adicionar os TextBoxes que poderão ser editados manualmente ou via designer
            textBoxesEdicao.Add(txtNomeCurso);
            textBoxesEdicao.Add(txtNomeCoordenador);
            textBoxesEdicao.Add(txtCargaHoraria);

            // Inicializa dados
            CarregarCurso();
        }

        private void btn_fechar_visualizarcurso_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a aplicação inteira
            }
        }

        private void btn_minimizar_visualizarcurso_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_visualizarcurso_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Maximized;
        }

        private void btn_proximo_visualizarcurso_Click(object sender, EventArgs e)
        {
            if (paginaAtual < totalPaginas - 1)
            {
                paginaAtual++;
                ExibirDadosPagina();
            }
        }

        private void btn_anterior_visualizarcurso_Click(object sender, EventArgs e)
        {
            if (paginaAtual > 0)
            {
                paginaAtual--;
                ExibirDadosPagina();
            }
        }

        private void btn_editar_visualizarcurso_Click(object sender, EventArgs e)
        {
            modoEdicao = true;
            btn_salvar_visualizarcurso.Visible = true;

            foreach (var txt in textBoxesEdicao)
            {
                txt.ReadOnly = false;
                txt.BackColor = Color.LightYellow;
            }

            MessageBox.Show("Modo de edição ativado. Dê duplo clique nos campos para editá-los.", "Modo Edição", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btn_excluir_visualizarcurso_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
               "Deseja realmente excluir este curso/coordenador?",
               "Confirmação de Exclusão",
               MessageBoxButtons.YesNo,
               MessageBoxIcon.Warning);

            if (resultado == DialogResult.Yes)
            {
                try
                {
                    int idSelecionado = ObterIdCursoSelecionado();
                    cmdBanco.ExcluirCursoOuCoordenador(idSelecionado);
                    MessageBox.Show("Registro excluído com sucesso!", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    CarregarCurso();
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Erro ao excluir o registro: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void btn_salvar_visualizarcurso_Click(object sender, EventArgs e)
        {
            try
            {
                foreach (var txtBox in textBoxesEdicao)
                {
                    FinalizarEdicao(txtBox);
                }

                foreach (DataRow row in dadosCurso.Rows)
                {
                    if (row.RowState == DataRowState.Modified)
                    {
                        cmdBanco.AtualizarCursoCoordenador(
                            Convert.ToInt32(row["ID_Curso"]),
                            row["NomeCurso"].ToString(),
                            row["NomeCoordenador"].ToString(),
                            Convert.ToInt32(row["CargaHoraria"])
                        );
                    }
                }

                MessageBox.Show("Alterações salvas com sucesso!", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);

                modoEdicao = false;
                btn_salvar_visualizarcurso.Visible = false;

                foreach (var txt in textBoxesEdicao)
                {
                    txt.ReadOnly = true;
                    txt.BackColor = Color.White;
                }

                CarregarCurso();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao salvar alterações: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Exemplo: método para carregar os dados na tela e paginar
        private void CarregarCurso()
        {
            dadosCurso = cmdBanco.ObterDadosCursoCoordenador();

            totalPaginas = (int)Math.Ceiling(dadosCurso.Rows.Count / 1.0); // supondo 1 registro por página, ajustar conforme necessário
            paginaAtual = 0;

            ExibirDadosPagina();
        }

        // Exibir os dados da página atual nos controles TextBox
        private void ExibirDadosPagina()
        {
            if (dadosCurso.Rows.Count == 0) return;

            var row = dadosCurso.Rows[paginaAtual];

            txtNomeCurso.Text = row["NomeCurso"].ToString();
            txtNomeCoordenador.Text = row["NomeCoordenador"].ToString();
            txtCargaHoraria.Text = row["CargaHoraria"].ToString();

            // Atualizar algum label que exibe a página, se houver
            lblPagina.Text = $"Página {paginaAtual + 1} de {totalPaginas}";
        }

        // Simulação para obter o Id do curso/coordenador selecionado (implemente conforme sua UI)
        private int ObterIdCursoSelecionado()
        {
            if (dadosCurso.Rows.Count == 0) return -1;
            return Convert.ToInt32(dadosCurso.Rows[paginaAtual]["ID_Curso"]);
        }

        // Finaliza edição do TextBox, atualizando o DataTable (simples exemplo)
        private void FinalizarEdicao(TextBox txtBox)
        {
            if (txtBox == txtNomeCurso)
            {
                dadosCurso.Rows[paginaAtual]["NomeCurso"] = txtBox.Text;
            }
            else if (txtBox == txtNomeCoordenador)
            {
                dadosCurso.Rows[paginaAtual]["NomeCoordenador"] = txtBox.Text;
            }
            else if (txtBox == txtCargaHoraria)
            {
                if (int.TryParse(txtBox.Text, out int carga))
                {
                    dadosCurso.Rows[paginaAtual]["CargaHoraria"] = carga;
                }
                else
                {
                    MessageBox.Show("Carga horária inválida!", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtBox.Text = dadosCurso.Rows[paginaAtual]["CargaHoraria"].ToString();
                }
            }
        }
    }
}
