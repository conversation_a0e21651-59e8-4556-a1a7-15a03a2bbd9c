using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using ProjetoIntegrador.Classes;

namespace ProjetoIntegrador.Diretor.D_VisualizarCursos
{
    public partial class D_VisualizarCursos : Form
    {
        private comandosbanco cmdBanco = new comandosbanco();
        private DataTable dadosCursos;
        private int paginaAtual = 0;
        private int registrosPorPagina = 9;
        private int totalPaginas = 0;
        private bool modoEdicao = false;
        private List<Label> labelsCurso = new List<Label>();
        private List<Label> labelsAulas = new List<Label>();
        private List<Label> labelsInicio = new List<Label>();
        private List<Label> labelsFim = new List<Label>();
        private List<TextBox> textBoxesEdicao = new List<TextBox>();
        private List<CheckBox> checkBoxes = new List<CheckBox>();
        private HashSet<int> itensSelecionadosGlobal = new HashSet<int>(); // Para manter seleções entre páginas

        public D_VisualizarCursos()
        {
            InitializeComponent();
            InicializarComponentes();
            CarregarCursos();
        }

        private void InicializarComponentes()
        {
            // Inicializar lista de checkboxes
            checkBoxes.AddRange(new CheckBox[] {
                cb_linha1_visualizarcurso,
                cb_linha2_visualizarcurso,
                cb_linha3_visualizarcurso,
                cb_linha4_visualizarcurso,
                cb_linha5_visualizarcurso,
                cb_linha6_visualizarcurso,
                cb_linha7_visualizarcurso,
                cb_linha8_visualizarcurso,
                cb_linha9_visualizarcurso
            });

            // Criar labels para exibir dados
            CriarLabelsParaDados();

            // Configurar botão salvar como invisível inicialmente
            btn_salvar_visualizarcurso.Visible = false;
        }

        private void CriarLabelsParaDados()
        {
            for (int i = 0; i < registrosPorPagina; i++)
            {
                // Labels para Curso
                Label lblCurso = new Label();
                lblCurso.AutoSize = false;
                lblCurso.Size = new Size(280, 40);
                lblCurso.TextAlign = ContentAlignment.MiddleLeft;
                lblCurso.Padding = new Padding(25, 0, 0, 0);
                lblCurso.BackColor = Color.Transparent;
                lblCurso.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                lblCurso.DoubleClick += (s, e) => IniciarEdicao(s as Label, "NOME_CURSO");
                pnl_grade1.Controls.Add(lblCurso, 0, i + 1);
                labelsCurso.Add(lblCurso);

                // Labels para Aulas
                Label lblAulas = new Label();
                lblAulas.AutoSize = false;
                lblAulas.Size = new Size(190, 40);
                lblAulas.TextAlign = ContentAlignment.MiddleCenter;
                lblAulas.BackColor = Color.Transparent;
                lblAulas.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                lblAulas.DoubleClick += (s, e) => IniciarEdicao(s as Label, "QTD_AULAS");
                pnl_grade1.Controls.Add(lblAulas, 1, i + 1);
                labelsAulas.Add(lblAulas);

                // Labels para Início
                Label lblInicio = new Label();
                lblInicio.AutoSize = false;
                lblInicio.Size = new Size(190, 40);
                lblInicio.TextAlign = ContentAlignment.MiddleCenter;
                lblInicio.BackColor = Color.Transparent;
                lblInicio.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                lblInicio.DoubleClick += (s, e) => IniciarEdicao(s as Label, "INICIOPERIODO");
                pnl_grade1.Controls.Add(lblInicio, 2, i + 1);
                labelsInicio.Add(lblInicio);

                // Labels para Fim
                Label lblFim = new Label();
                lblFim.AutoSize = false;
                lblFim.Size = new Size(190, 40);
                lblFim.TextAlign = ContentAlignment.MiddleCenter;
                lblFim.BackColor = Color.Transparent;
                lblFim.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                lblFim.DoubleClick += (s, e) => IniciarEdicao(s as Label, "FIMPERIODO");
                pnl_grade1.Controls.Add(lblFim, 3, i + 1);
                labelsFim.Add(lblFim);
            }
        }

        private void CarregarCursos()
        {
            try
            {
                dadosCursos = cmdBanco.ConsultarCursos();
                totalPaginas = (int)Math.Ceiling((double)dadosCursos.Rows.Count / registrosPorPagina);
                if (totalPaginas == 0) totalPaginas = 1;

                ExibirDadosPagina();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar cursos: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExibirDadosPagina()
        {
            // Suspender layout para melhorar performance
            pnl_grade1.SuspendLayout();

            try
            {
                // Salvar seleções da página atual antes de trocar
                SalvarSelecoesPaginaAtual();

                // Limpar e ocultar todos os controles de uma vez
                for (int i = 0; i < registrosPorPagina; i++)
                {
                    checkBoxes[i].Visible = false;
                    checkBoxes[i].Checked = false;
                    checkBoxes[i].Tag = null;

                    labelsCurso[i].Text = "";
                    labelsCurso[i].Tag = null;

                    labelsAulas[i].Text = "";
                    labelsAulas[i].Tag = null;

                    labelsInicio[i].Text = "";
                    labelsInicio[i].Tag = null;

                    labelsFim[i].Text = "";
                    labelsFim[i].Tag = null;
                }

                if (dadosCursos == null || dadosCursos.Rows.Count == 0)
                    return;

                int inicioIndice = paginaAtual * registrosPorPagina;
                int fimIndice = Math.Min(inicioIndice + registrosPorPagina, dadosCursos.Rows.Count);

                // Preencher apenas os dados necessários
                for (int i = inicioIndice; i < fimIndice; i++)
                {
                    int indiceLabel = i - inicioIndice;
                    DataRow row = dadosCursos.Rows[i];

                    // Configurar checkbox
                    checkBoxes[indiceLabel].Visible = true;
                    checkBoxes[indiceLabel].Tag = row;

                    // Restaurar seleção se estava selecionado globalmente
                    int idCurso = Convert.ToInt32(row["ID_CURSO"]);
                    checkBoxes[indiceLabel].Checked = itensSelecionadosGlobal.Contains(idCurso);

                    // Preencher dados nos labels de forma otimizada
                    labelsCurso[indiceLabel].Text = row["NOME_CURSO"].ToString();
                    labelsCurso[indiceLabel].Tag = row;

                    labelsAulas[indiceLabel].Text = row["QTD_AULAS"].ToString();
                    labelsAulas[indiceLabel].Tag = row;

                    // Formatar datas para exibição
                    DateTime inicioperiodo = Convert.ToDateTime(row["INICIOPERIODO"]);
                    DateTime fimperiodo = Convert.ToDateTime(row["FIMPERIODO"]);

                    labelsInicio[indiceLabel].Text = inicioperiodo.ToString("dd/MM/yyyy");
                    labelsInicio[indiceLabel].Tag = row;

                    labelsFim[indiceLabel].Text = fimperiodo.ToString("dd/MM/yyyy");
                    labelsFim[indiceLabel].Tag = row;
                }

                AtualizarControlePaginacao();
            }
            finally
            {
                // Retomar layout
                pnl_grade1.ResumeLayout(true);
            }
        }

        private void SalvarSelecoesPaginaAtual()
        {
            for (int i = 0; i < registrosPorPagina; i++)
            {
                if (checkBoxes[i].Visible && checkBoxes[i].Tag != null)
                {
                    DataRow row = (DataRow)checkBoxes[i].Tag;
                    int idCurso = Convert.ToInt32(row["ID_CURSO"]);

                    if (checkBoxes[i].Checked)
                    {
                        itensSelecionadosGlobal.Add(idCurso);
                    }
                    else
                    {
                        itensSelecionadosGlobal.Remove(idCurso);
                    }
                }
            }
        }

        /// <summary>
        /// Obtém todos os itens selecionados de todas as páginas
        /// </summary>
        private List<DataRow> ObterTodosItensSelecionados()
        {
            // Salvar seleções da página atual
            SalvarSelecoesPaginaAtual();

            // Buscar todas as linhas selecionadas no DataTable
            var itensSelecionados = new List<DataRow>();

            foreach (DataRow row in dadosCursos.Rows)
            {
                int idCurso = Convert.ToInt32(row["ID_CURSO"]);
                if (itensSelecionadosGlobal.Contains(idCurso))
                {
                    itensSelecionados.Add(row);
                }
            }

            return itensSelecionados;
        }

        private void AtualizarControlePaginacao()
        {
            lbl_paginacao_visualizarcoordenadores.Text = $"Página {paginaAtual + 1} de {totalPaginas}";
            btn_anterior_visualizarcurso.Enabled = paginaAtual > 0;
            btn_proximo_visualizarcurso.Enabled = paginaAtual < totalPaginas - 1;
        }

        private void btn_anterior_visualizarcurso_Click(object sender, EventArgs e)
        {
            if (paginaAtual > 0)
            {
                paginaAtual--;
                ExibirDadosPagina();
            }
        }

        private void btn_proximo_visualizarcurso_Click(object sender, EventArgs e)
        {
            if (paginaAtual < totalPaginas - 1)
            {
                paginaAtual++;
                ExibirDadosPagina();
            }
        }

        private void btn_fechar_visualizarcurso_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        private void btn_minimizar_visualizarcurso_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_visualizarcurso_Click(object sender, EventArgs e)
        {
            ToggleMaximizeWindow();
        }

        private void ToggleMaximizeWindow()
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                this.WindowState = FormWindowState.Normal;
                this.StartPosition = FormStartPosition.CenterScreen;
                this.CenterToScreen();
            }
            else
            {
                this.WindowState = FormWindowState.Maximized;
            }
        }

        private void IniciarEdicao(Label label, string campo)
        {
            if (!modoEdicao)
            {
                MessageBox.Show("Ative o modo de edição primeiro clicando no botão 'Editar'.", "Modo Edição", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (label.Tag == null) return;

            DataRow row = (DataRow)label.Tag;

            // Criar TextBox para edição
            TextBox txtEdicao = new TextBox();
            txtEdicao.Text = label.Text;
            txtEdicao.Size = label.Size;
            txtEdicao.Location = label.Location;
            txtEdicao.Font = label.Font;
            txtEdicao.Tag = new { Label = label, Row = row, Campo = campo };

            // Configurar formato específico para datas
            if (campo == "INICIOPERIODO" || campo == "FIMPERIODO")
            {
                txtEdicao.PlaceholderText = "dd/MM/yyyy";
            }

            // Eventos do TextBox
            txtEdicao.KeyDown += (s, e) =>
            {
                if (e.KeyCode == Keys.Enter)
                {
                    FinalizarEdicao(txtEdicao);
                }
                else if (e.KeyCode == Keys.Escape)
                {
                    CancelarEdicao(txtEdicao);
                }
            };

            txtEdicao.LostFocus += (s, e) => FinalizarEdicao(txtEdicao);

            // Adicionar TextBox ao painel na mesma posição do label
            TableLayoutPanelCellPosition posicao = pnl_grade1.GetCellPosition(label);
            pnl_grade1.Controls.Add(txtEdicao, posicao.Column, posicao.Row);

            // Ocultar label e focar no TextBox
            label.Visible = false;
            txtEdicao.Focus();
            txtEdicao.SelectAll();

            textBoxesEdicao.Add(txtEdicao);
        }

        private void btn_editar_visualizarcurso_Click(object sender, EventArgs e)
        {
            modoEdicao = true;
            btn_salvar_visualizarcurso.Visible = true;
            MessageBox.Show("Modo de edição ativado. Dê duplo clique nos campos para editá-los.", "Modo Edição", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btn_salvar_visualizarcurso_Click(object sender, EventArgs e)
        {
            try
            {
                // Finalizar todas as edições pendentes
                var textBoxesParaFinalizar = new List<TextBox>(textBoxesEdicao);
                foreach (var txtBox in textBoxesParaFinalizar)
                {
                    FinalizarEdicao(txtBox);
                }

                // Salvar alterações no banco de dados
                foreach (DataRow row in dadosCursos.Rows)
                {
                    if (row.RowState == DataRowState.Modified)
                    {
                        variaveisbanco modelo = new variaveisbanco
                        {
                            nomecurso = row["NOME_CURSO"].ToString(),
                            quantidadeaulas = row["QTD_AULAS"].ToString(),
                            inicioperiodo = Convert.ToDateTime(row["INICIOPERIODO"]),
                            fimperiodo = Convert.ToDateTime(row["FIMPERIODO"])
                        };

                        int idCurso = Convert.ToInt32(row["ID_CURSO"]);
                        cmdBanco.AtualizarCurso(modelo, idCurso);
                    }
                }

                MessageBox.Show("Alterações salvas com sucesso!", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Sair do modo de edição
                modoEdicao = false;
                btn_salvar_visualizarcurso.Visible = false;

                // Recarregar dados
                CarregarCursos();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao salvar alterações: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btn_excluir_visualizarcurso_Click(object sender, EventArgs e)
        {
            var itensSelecionados = ObterTodosItensSelecionados();

            if (itensSelecionados.Count == 0)
            {
                MessageBox.Show("Selecione pelo menos um curso para excluir.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            DialogResult resultado = MessageBox.Show(
                $"Deseja realmente excluir {itensSelecionados.Count} curso(s) selecionado(s)?",
                "Confirmação de Exclusão",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (resultado == DialogResult.Yes)
            {
                try
                {
                    foreach (DataRow row in itensSelecionados)
                    {
                        int idCurso = Convert.ToInt32(row["ID_CURSO"]);
                        cmdBanco.DeletarCurso(idCurso);
                    }

                    MessageBox.Show($"{itensSelecionados.Count} curso(s) excluído(s) com sucesso!", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Limpar seleções e recarregar dados
                    itensSelecionadosGlobal.Clear();
                    CarregarCursos();
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Erro ao excluir curso(s): " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void FinalizarEdicao(TextBox txtEdicao)
        {
            try
            {
                var tagInfo = txtEdicao.Tag as dynamic;
                Label label = tagInfo.Label;
                DataRow row = tagInfo.Row;
                string campo = tagInfo.Campo;

                string novoValor = txtEdicao.Text.Trim();

                // Validar formato de data se necessário
                if (campo == "INICIOPERIODO" || campo == "FIMPERIODO")
                {
                    if (!DateTime.TryParseExact(novoValor, "dd/MM/yyyy", null, System.Globalization.DateTimeStyles.None, out DateTime data))
                    {
                        MessageBox.Show("Formato de data inválido. Use dd/MM/yyyy", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtEdicao.Focus();
                        return;
                    }
                    // Converter para formato do banco
                    novoValor = data.ToString("yyyy-MM-dd");
                }

                // Validar quantidade de aulas
                if (campo == "QTD_AULAS")
                {
                    if (!int.TryParse(novoValor, out int qtdAulas) || qtdAulas <= 0)
                    {
                        MessageBox.Show("Quantidade de aulas deve ser um número inteiro positivo.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtEdicao.Focus();
                        return;
                    }
                }

                // Atualizar o DataRow
                row[campo] = novoValor;

                // Atualizar o label com formato correto para exibição
                if (campo == "INICIOPERIODO" || campo == "FIMPERIODO")
                {
                    DateTime data = DateTime.Parse(novoValor);
                    label.Text = data.ToString("dd/MM/yyyy");
                }
                else
                {
                    label.Text = novoValor;
                }

                label.Visible = true;

                // Remover TextBox
                pnl_grade1.Controls.Remove(txtEdicao);
                textBoxesEdicao.Remove(txtEdicao);
                txtEdicao.Dispose();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao finalizar edição: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelarEdicao(TextBox txtEdicao)
        {
            try
            {
                var tagInfo = txtEdicao.Tag as dynamic;
                Label label = tagInfo.Label;

                // Restaurar label
                label.Visible = true;

                // Remover TextBox
                pnl_grade1.Controls.Remove(txtEdicao);
                textBoxesEdicao.Remove(txtEdicao);
                txtEdicao.Dispose();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao cancelar edição: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
