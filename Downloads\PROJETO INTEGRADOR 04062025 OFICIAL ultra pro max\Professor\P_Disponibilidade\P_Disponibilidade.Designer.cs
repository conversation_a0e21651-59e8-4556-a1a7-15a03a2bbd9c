﻿namespace ProjetoIntegrador.Professor
{
    partial class P_IDisponibilidade
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(P_IDisponibilidade));
            pnl_header = new Panel();
            btn_voltar_disponibilidade = new Button();
            btn_minimizar_disponibilidade = new Button();
            btn_maximizar_disponibilidade = new Button();
            lbl_titulo = new Label();
            btn_fechar_disponibilidade = new Button();
            lbl_inserirdisponibilidade = new Label();
            txt_horarioinicio_disponibilidade = new TextBox();
            txt_horariofim_disponibilidade = new TextBox();
            cb_selecionardias_disponibilidade = new ComboBox();
            label2 = new Label();
            btn_inserir = new Button();
            label1 = new Label();
            label3 = new Label();
            pnl_header.SuspendLayout();
            SuspendLayout();
            // 
            // pnl_header
            // 
            pnl_header.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            pnl_header.BackColor = Color.MidnightBlue;
            pnl_header.Controls.Add(btn_voltar_disponibilidade);
            pnl_header.Controls.Add(btn_minimizar_disponibilidade);
            pnl_header.Controls.Add(btn_maximizar_disponibilidade);
            pnl_header.Controls.Add(lbl_titulo);
            pnl_header.Controls.Add(btn_fechar_disponibilidade);
            pnl_header.Location = new Point(0, 0);
            pnl_header.Name = "pnl_header";
            pnl_header.Size = new Size(1280, 63);
            pnl_header.TabIndex = 7;
            // 
            // btn_voltar_disponibilidade
            // 
            btn_voltar_disponibilidade.BackColor = Color.MidnightBlue;
            btn_voltar_disponibilidade.BackgroundImage = (Image)resources.GetObject("btn_voltar_disponibilidade.BackgroundImage");
            btn_voltar_disponibilidade.BackgroundImageLayout = ImageLayout.Zoom;
            btn_voltar_disponibilidade.FlatAppearance.BorderSize = 0;
            btn_voltar_disponibilidade.FlatStyle = FlatStyle.Flat;
            btn_voltar_disponibilidade.Location = new Point(35, 18);
            btn_voltar_disponibilidade.Name = "btn_voltar_disponibilidade";
            btn_voltar_disponibilidade.Size = new Size(30, 21);
            btn_voltar_disponibilidade.TabIndex = 23;
            btn_voltar_disponibilidade.UseVisualStyleBackColor = false;
            btn_voltar_disponibilidade.Click += btn_voltar_disponibilidade_Click;
            // 
            // btn_minimizar_disponibilidade
            // 
            btn_minimizar_disponibilidade.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btn_minimizar_disponibilidade.BackgroundImage = (Image)resources.GetObject("btn_minimizar_disponibilidade.BackgroundImage");
            btn_minimizar_disponibilidade.BackgroundImageLayout = ImageLayout.Stretch;
            btn_minimizar_disponibilidade.FlatAppearance.BorderSize = 0;
            btn_minimizar_disponibilidade.FlatStyle = FlatStyle.Flat;
            btn_minimizar_disponibilidade.Location = new Point(1173, 22);
            btn_minimizar_disponibilidade.Name = "btn_minimizar_disponibilidade";
            btn_minimizar_disponibilidade.Size = new Size(21, 19);
            btn_minimizar_disponibilidade.TabIndex = 22;
            btn_minimizar_disponibilidade.UseVisualStyleBackColor = true;
            btn_minimizar_disponibilidade.Click += btn_minimizar_Click;
            // 
            // btn_maximizar_disponibilidade
            // 
            btn_maximizar_disponibilidade.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btn_maximizar_disponibilidade.BackgroundImage = (Image)resources.GetObject("btn_maximizar_disponibilidade.BackgroundImage");
            btn_maximizar_disponibilidade.BackgroundImageLayout = ImageLayout.Zoom;
            btn_maximizar_disponibilidade.FlatAppearance.BorderSize = 0;
            btn_maximizar_disponibilidade.FlatStyle = FlatStyle.Flat;
            btn_maximizar_disponibilidade.Location = new Point(1209, 18);
            btn_maximizar_disponibilidade.Name = "btn_maximizar_disponibilidade";
            btn_maximizar_disponibilidade.Size = new Size(19, 23);
            btn_maximizar_disponibilidade.TabIndex = 21;
            btn_maximizar_disponibilidade.UseVisualStyleBackColor = true;
            btn_maximizar_disponibilidade.Click += btn_maximizar_Click;
            // 
            // lbl_titulo
            // 
            lbl_titulo.AutoSize = true;
            lbl_titulo.Font = new Font("Segoe UI", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_titulo.ForeColor = SystemColors.ButtonHighlight;
            lbl_titulo.Location = new Point(632, 18);
            lbl_titulo.Name = "lbl_titulo";
            lbl_titulo.Size = new Size(62, 25);
            lbl_titulo.TabIndex = 1;
            lbl_titulo.Text = "SEHD";
            // 
            // btn_fechar_disponibilidade
            // 
            btn_fechar_disponibilidade.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btn_fechar_disponibilidade.BackColor = Color.MidnightBlue;
            btn_fechar_disponibilidade.BackgroundImage = (Image)resources.GetObject("btn_fechar_disponibilidade.BackgroundImage");
            btn_fechar_disponibilidade.BackgroundImageLayout = ImageLayout.Zoom;
            btn_fechar_disponibilidade.FlatAppearance.BorderSize = 0;
            btn_fechar_disponibilidade.FlatStyle = FlatStyle.Flat;
            btn_fechar_disponibilidade.Location = new Point(1234, 18);
            btn_fechar_disponibilidade.Name = "btn_fechar_disponibilidade";
            btn_fechar_disponibilidade.Size = new Size(34, 21);
            btn_fechar_disponibilidade.TabIndex = 20;
            btn_fechar_disponibilidade.UseVisualStyleBackColor = false;
            btn_fechar_disponibilidade.Click += btn_fechar_Click;
            // 
            // lbl_inserirdisponibilidade
            // 
            lbl_inserirdisponibilidade.AutoSize = true;
            lbl_inserirdisponibilidade.Font = new Font("Segoe UI", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lbl_inserirdisponibilidade.ForeColor = Color.MidnightBlue;
            lbl_inserirdisponibilidade.Location = new Point(561, 224);
            lbl_inserirdisponibilidade.Name = "lbl_inserirdisponibilidade";
            lbl_inserirdisponibilidade.Size = new Size(211, 25);
            lbl_inserirdisponibilidade.TabIndex = 37;
            lbl_inserirdisponibilidade.Text = "Inserir disponibilidade";
            // 
            // txt_horarioinicio_disponibilidade
            // 
            txt_horarioinicio_disponibilidade.Location = new Point(466, 296);
            txt_horarioinicio_disponibilidade.Name = "txt_horarioinicio_disponibilidade";
            txt_horarioinicio_disponibilidade.PlaceholderText = "00:00";
            txt_horarioinicio_disponibilidade.Size = new Size(404, 23);
            txt_horarioinicio_disponibilidade.TabIndex = 38;
            // 
            // txt_horariofim_disponibilidade
            // 
            txt_horariofim_disponibilidade.Location = new Point(466, 365);
            txt_horariofim_disponibilidade.Name = "txt_horariofim_disponibilidade";
            txt_horariofim_disponibilidade.PlaceholderText = "00:00";
            txt_horariofim_disponibilidade.Size = new Size(404, 23);
            txt_horariofim_disponibilidade.TabIndex = 39;
            // 
            // cb_selecionardias_disponibilidade
            // 
            cb_selecionardias_disponibilidade.BackColor = Color.White;
            cb_selecionardias_disponibilidade.FormattingEnabled = true;
            cb_selecionardias_disponibilidade.Location = new Point(466, 429);
            cb_selecionardias_disponibilidade.Name = "cb_selecionardias_disponibilidade";
            cb_selecionardias_disponibilidade.Size = new Size(404, 23);
            cb_selecionardias_disponibilidade.TabIndex = 40;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Font = new Font("Segoe UI Semibold", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            label2.ForeColor = Color.DimGray;
            label2.Location = new Point(466, 409);
            label2.Name = "label2";
            label2.Size = new Size(33, 17);
            label2.TabIndex = 41;
            label2.Text = "Dias";
            // 
            // btn_inserir
            // 
            btn_inserir.BackColor = Color.Transparent;
            btn_inserir.BackgroundImage = (Image)resources.GetObject("btn_inserir.BackgroundImage");
            btn_inserir.BackgroundImageLayout = ImageLayout.Zoom;
            btn_inserir.FlatAppearance.BorderSize = 0;
            btn_inserir.FlatStyle = FlatStyle.Flat;
            btn_inserir.ForeColor = SystemColors.ControlLightLight;
            btn_inserir.Location = new Point(605, 486);
            btn_inserir.Name = "btn_inserir";
            btn_inserir.Size = new Size(125, 33);
            btn_inserir.TabIndex = 42;
            btn_inserir.Text = "Inserir";
            btn_inserir.UseVisualStyleBackColor = false;
            btn_inserir.Click += btn_inserir_Click;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Font = new Font("Segoe UI Semibold", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            label1.ForeColor = Color.DimGray;
            label1.Location = new Point(466, 345);
            label1.Name = "label1";
            label1.Size = new Size(89, 17);
            label1.TabIndex = 43;
            label1.Text = "Horário / Fim";
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Font = new Font("Segoe UI Semibold", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            label3.ForeColor = Color.DimGray;
            label3.Location = new Point(466, 276);
            label3.Name = "label3";
            label3.Size = new Size(99, 17);
            label3.TabIndex = 44;
            label3.Text = "Horário / Início";
            // 
            // P_IDisponibilidade
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1280, 702);
            Controls.Add(label3);
            Controls.Add(label1);
            Controls.Add(btn_inserir);
            Controls.Add(label2);
            Controls.Add(cb_selecionardias_disponibilidade);
            Controls.Add(txt_horariofim_disponibilidade);
            Controls.Add(txt_horarioinicio_disponibilidade);
            Controls.Add(lbl_inserirdisponibilidade);
            Controls.Add(pnl_header);
            FormBorderStyle = FormBorderStyle.None;
            Name = "P_IDisponibilidade";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "P_Disponibilidade";
            Load += P_IDisponibilidade_Load;
            pnl_header.ResumeLayout(false);
            pnl_header.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Panel pnl_header;
        private Button btn_voltar_disponibilidade;
        private Button btn_minimizar_disponibilidade;
        private Button btn_maximizar_disponibilidade;
        private Label lbl_titulo;
        private Button btn_fechar_disponibilidade;
        private Label lbl_inserirdisponibilidade;
        private TextBox txt_horarioinicio_disponibilidade;
        private TextBox txt_horariofim_disponibilidade;
        private ComboBox cb_selecionardias_disponibilidade;
        private Label label2;
        private Button btn_inserir;
        private Label label1;
        private Label label3;
    }
}