using System;
using System.Media;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace ProjetoIntegrador.Classes
{
    /// <summary>
    /// Classe utilitária para validação de texto e caracteres especiais
    /// </summary>
    public static class ValidadorTexto
    {
        /// <summary>
        /// Caracteres especiais não permitidos em campos de texto
        /// </summary>
        private static readonly string CaracteresEspeciaisProibidos = @"[<>""'%;()&+\-*/=?^`{}|~[\]\\]";

        /// <summary>
        /// Valida se o texto contém caracteres especiais não permitidos
        /// </summary>
        /// <param name="texto">Texto a ser validado</param>
        /// <returns>True se o texto é válido, False se contém caracteres especiais</returns>
        public static bool ValidarTexto(string texto)
        {
            if (string.IsNullOrWhiteSpace(texto))
                return false;

            // Verificar se contém caracteres especiais proibidos
            return !Regex.IsMatch(texto, CaracteresEspeciaisProibidos);
        }

        /// <summary>
        /// Valida texto e exibe mensagem de erro se inválido
        /// </summary>
        /// <param name="texto">Texto a ser validado</param>
        /// <param name="nomeCampo">Nome do campo para exibir na mensagem</param>
        /// <param name="controle">Controle para focar em caso de erro</param>
        /// <returns>True se válido, False se inválido</returns>
        public static bool ValidarTextoComMensagem(string texto, string nomeCampo, Control controle = null)
        {
            if (string.IsNullOrWhiteSpace(texto))
            {
                MessageBox.Show($"O campo '{nomeCampo}' não pode estar vazio.", 
                               "Campo Obrigatório", 
                               MessageBoxButtons.OK, 
                               MessageBoxIcon.Warning);
                controle?.Focus();
                return false;
            }

            if (!ValidarTexto(texto))
            {
                MessageBox.Show($"O campo '{nomeCampo}' contém caracteres especiais não permitidos.\n\n" +
                               "Caracteres não permitidos: < > \" ' % ; ( ) & + - * / = ? ^ ` { } | ~ [ ] \\", 
                               "Caracteres Inválidos", 
                               MessageBoxButtons.OK, 
                               MessageBoxIcon.Warning);
                controle?.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// Valida texto durante a digitação (KeyPress)
        /// </summary>
        /// <param name="e">Argumentos do evento KeyPress</param>
        public static void ValidarCaracterDigitacao(KeyPressEventArgs e)
        {
            // Permitir teclas de controle (Backspace, Delete, etc.)
            if (char.IsControl(e.KeyChar))
                return;

            // Verificar se o caractere é um caractere especial proibido
            string caracterString = e.KeyChar.ToString();
            if (Regex.IsMatch(caracterString, CaracteresEspeciaisProibidos))
            {
                e.Handled = true; // Bloquear o caractere
                
                // Opcional: Mostrar tooltip ou som de erro
                SystemSounds.Beep.Play();
            }
        }

        /// <summary>
        /// Valida apenas números (para campos como RA)
        /// </summary>
        /// <param name="e">Argumentos do evento KeyPress</param>
        /// <param name="tamanhoMaximo">Tamanho máximo permitido</param>
        /// <param name="textBox">TextBox para verificar o tamanho atual</param>
        public static void ValidarApenasNumeros(KeyPressEventArgs e, int tamanhoMaximo = 0, TextBox textBox = null)
        {
            // Permitir apenas números, backspace e delete
            if (!char.IsDigit(e.KeyChar) && e.KeyChar != (char)Keys.Back)
            {
                e.Handled = true;
                SystemSounds.Beep.Play();
                return;
            }

            // Verificar tamanho máximo se especificado
            if (tamanhoMaximo > 0 && textBox != null)
            {
                if (textBox.Text.Length >= tamanhoMaximo && e.KeyChar != (char)Keys.Back)
                {
                    e.Handled = true;
                    SystemSounds.Beep.Play();
                }
            }
        }

        /// <summary>
        /// Valida formato de horário (HH:mm)
        /// </summary>
        /// <param name="horario">Horário a ser validado</param>
        /// <param name="nomeCampo">Nome do campo para mensagem de erro</param>
        /// <param name="controle">Controle para focar em caso de erro</param>
        /// <returns>True se válido, False se inválido</returns>
        public static bool ValidarHorario(string horario, string nomeCampo, Control controle = null)
        {
            if (string.IsNullOrWhiteSpace(horario))
            {
                MessageBox.Show($"O campo '{nomeCampo}' não pode estar vazio.", 
                               "Campo Obrigatório", 
                               MessageBoxButtons.OK, 
                               MessageBoxIcon.Warning);
                controle?.Focus();
                return false;
            }

            // Validar formato HH:mm
            if (!Regex.IsMatch(horario, @"^([01]?[0-9]|2[0-3]):[0-5][0-9]$"))
            {
                MessageBox.Show($"O campo '{nomeCampo}' deve estar no formato HH:mm (ex: 08:30).", 
                               "Formato Inválido", 
                               MessageBoxButtons.OK, 
                               MessageBoxIcon.Warning);
                controle?.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// Remove caracteres especiais de um texto
        /// </summary>
        /// <param name="texto">Texto a ser limpo</param>
        /// <returns>Texto sem caracteres especiais</returns>
        public static string RemoverCaracteresEspeciais(string texto)
        {
            if (string.IsNullOrWhiteSpace(texto))
                return string.Empty;

            return Regex.Replace(texto, CaracteresEspeciaisProibidos, "");
        }

        /// <summary>
        /// Valida se um texto contém apenas letras, números e espaços
        /// </summary>
        /// <param name="texto">Texto a ser validado</param>
        /// <returns>True se válido, False se inválido</returns>
        public static bool ValidarTextoAlfanumerico(string texto)
        {
            if (string.IsNullOrWhiteSpace(texto))
                return false;

            // Permitir apenas letras, números, espaços e alguns caracteres básicos como ponto e vírgula
            return Regex.IsMatch(texto, @"^[a-zA-ZÀ-ÿ0-9\s.,]+$");
        }
    }
}
