using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using ProjetoIntegrador.Classes;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Button;

namespace ProjetoIntegrador.Professor.P_Disponibilidade
{
    public partial class P_VisualizarDisponibilidade : Form
    {
        // Estrutura para armazenar dados de disponibilidade por dia
        private Dictionary<string, DisponibilidadeInfo> disponibilidadesPorDia = new Dictionary<string, DisponibilidadeInfo>();
        private List<TextBox> textBoxesEdicao = new List<TextBox>();
        private bool modoEdicao = false;
        private comandosbanco cmdBanco = new comandosbanco();

        // Controles específicos para cada dia da semana
        private Dictionary<string, System.Windows.Forms.CheckBox> checkBoxesDias = new Dictionary<string, System.Windows.Forms.CheckBox>();
        private Dictionary<string, Label> labelsDias = new Dictionary<string, Label>();
        private Dictionary<string, Label> labelsHorarios = new Dictionary<string, Label>();

        // Classe para armazenar informações de disponibilidade
        private class DisponibilidadeInfo
        {
            public int IdDisponibilidade { get; set; }
            public string DiaSemana { get; set; }
            public TimeSpan? HorarioInicio { get; set; }
            public TimeSpan? HorarioFim { get; set; }
            public bool TemDisponibilidade => IdDisponibilidade > 0;
            public string HorarioCompleto => TemDisponibilidade && HorarioInicio.HasValue && HorarioFim.HasValue
                ? $"{HorarioInicio.Value:hh\\:mm} - {HorarioFim.Value:hh\\:mm}"
                : "Não disponível";
        }

        public P_VisualizarDisponibilidade()
        {
            InitializeComponent();
            InicializarComponentes();
            this.Load += P_VisualizarDisponibilidade_Load;
        }

        private void btn_fechar_visualizardisponibilidades_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto


        }

        private void btn_minimizar_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_Click(object sender, EventArgs e)
        {
            ToggleMaximizeWindow();
        }

        /// <summary>
        /// Alterna entre maximizar e restaurar a janela, centralizando quando restaura
        /// </summary>
        private void ToggleMaximizeWindow()
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                // Se está maximizada, restaura ao tamanho normal e centraliza
                this.WindowState = FormWindowState.Normal;
                this.StartPosition = FormStartPosition.CenterScreen;
                this.CenterToScreen();
            }
            else
            {
                // Se não está maximizada, maximiza
                this.WindowState = FormWindowState.Maximized;
            }
        }

        /// <summary>
        /// Inicializa os componentes necessários para o funcionamento do formulário
        /// </summary>
        private void InicializarComponentes()
        {
            // Criar controles específicos para cada dia da semana
            CriarControlesDiasSemana();
        }

        /// <summary>
        /// Cria controles específicos para cada dia da semana (Segunda a Sexta)
        /// </summary>
        private void CriarControlesDiasSemana()
        {
            string[] diasSemana = { "Segunda-feira", "Terça-feira", "Quarta-feira", "Quinta-feira", "Sexta-feira" };
            string[] nomesCheckboxes = {
                "chb_linha1_visualizardisponibilidade",
                "chb_linha2_visualizardisponibilidade",
                "chb_linha3_visualizardisponibilidade",
                "chb_linha4_visualizardisponibilidade",
                "chb_linha5_visualizardisponibilidade"
            };

            for (int i = 0; i < diasSemana.Length; i++)
            {
                string dia = diasSemana[i];

                // Criar checkbox para o dia
                System.Windows.Forms.CheckBox cb = new System.Windows.Forms.CheckBox();
                cb.AutoSize = true;
                cb.BackColor = Color.Transparent;
                cb.Name = nomesCheckboxes[i];
                cb.Size = new Size(15, 14);
                cb.TabIndex = i + 50;
                cb.UseVisualStyleBackColor = false;
                cb.Visible = true;

                // Adicionar checkbox ao painel
                pnl_disponibilidade.Controls.Add(cb, 0, i + 1);
                checkBoxesDias[dia] = cb;

                // Criar label para o dia da semana
                Label lblDia = new Label();
                lblDia.AutoSize = false;
                lblDia.Size = new Size(200, 40);
                lblDia.TextAlign = ContentAlignment.MiddleLeft;
                lblDia.Padding = new Padding(25, 0, 0, 0);
                lblDia.BackColor = Color.Transparent;
                lblDia.Font = new Font("Segoe UI", 10F, FontStyle.Regular);
                lblDia.ForeColor = Color.MidnightBlue;
                lblDia.Text = dia;
                lblDia.Tag = dia; // Armazenar o dia da semana no Tag
                lblDia.Visible = true;

                pnl_disponibilidade.Controls.Add(lblDia, 0, i + 1);
                labelsDias[dia] = lblDia;

                // Criar label para horários
                Label lblHorario = new Label();
                lblHorario.AutoSize = false;
                lblHorario.Size = new Size(250, 40);
                lblHorario.TextAlign = ContentAlignment.MiddleCenter;
                lblHorario.BackColor = Color.Transparent;
                lblHorario.Font = new Font("Segoe UI", 10F, FontStyle.Regular);
                lblHorario.ForeColor = Color.MidnightBlue;
                lblHorario.Text = "Não disponível";
                lblHorario.Tag = dia; // Armazenar o dia da semana no Tag
                lblHorario.DoubleClick += (s, e) => IniciarEdicaoHorario(s as Label, dia);
                lblHorario.Visible = true;

                pnl_disponibilidade.Controls.Add(lblHorario, 1, i + 1);
                labelsHorarios[dia] = lblHorario;

                // Inicializar dados de disponibilidade
                disponibilidadesPorDia[dia] = new DisponibilidadeInfo
                {
                    IdDisponibilidade = 0,
                    DiaSemana = dia,
                    HorarioInicio = null,
                    HorarioFim = null
                };
            }
        }

        /// <summary>
        /// Carrega as disponibilidades do professor logado
        /// </summary>
        private void CarregarDisponibilidades()
        {
            try
            {
                // Verificar se o professor está logado
                if (!SessaoUsuario.EhProfessor || !SessaoUsuario.IdProfessor.HasValue)
                {
                    MessageBox.Show("Erro: Professor não identificado. Faça login novamente.",
                                   "Erro de Sessão", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Consultar disponibilidades organizadas por dia da semana
                DataTable dados = cmdBanco.ConsultarDisponibilidadesPorDiaSemana(SessaoUsuario.IdProfessor.Value);

                // Limpar dados anteriores
                foreach (var dia in disponibilidadesPorDia.Keys.ToList())
                {
                    disponibilidadesPorDia[dia] = new DisponibilidadeInfo
                    {
                        IdDisponibilidade = 0,
                        DiaSemana = dia,
                        HorarioInicio = null,
                        HorarioFim = null
                    };
                }

                // Preencher com dados do banco
                foreach (DataRow row in dados.Rows)
                {
                    string dia = row["DIA_SEMANA"].ToString();
                    int idDisponibilidade = Convert.ToInt32(row["ID_DISPONIBILIDADE"]);

                    if (disponibilidadesPorDia.ContainsKey(dia))
                    {
                        disponibilidadesPorDia[dia].IdDisponibilidade = idDisponibilidade;

                        if (idDisponibilidade > 0)
                        {
                            string horarioInicioStr = row["HORARIO_INICIO"].ToString();
                            string horarioFimStr = row["HORARIO_FIM"].ToString();

                            if (TimeSpan.TryParse(horarioInicioStr, out TimeSpan inicio))
                                disponibilidadesPorDia[dia].HorarioInicio = inicio;

                            if (TimeSpan.TryParse(horarioFimStr, out TimeSpan fim))
                                disponibilidadesPorDia[dia].HorarioFim = fim;
                        }
                    }
                }

                // Atualizar interface
                AtualizarInterface();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar disponibilidades: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Atualiza a interface com os dados carregados
        /// </summary>
        private void AtualizarInterface()
        {
            foreach (var kvp in disponibilidadesPorDia)
            {
                string dia = kvp.Key;
                DisponibilidadeInfo info = kvp.Value;

                if (labelsHorarios.ContainsKey(dia))
                {
                    labelsHorarios[dia].Text = info.HorarioCompleto;
                    labelsHorarios[dia].Tag = info; // Armazenar info completa no Tag
                }

                if (checkBoxesDias.ContainsKey(dia))
                {
                    checkBoxesDias[dia].Enabled = info.TemDisponibilidade;
                    checkBoxesDias[dia].Checked = false;
                }
            }
        }

        private void P_VisualizarDisponibilidade_Load(object sender, EventArgs e)
        {
            CarregarDisponibilidades();
        }

        /// <summary>
        /// Inicia a edição de horário para um dia específico
        /// </summary>
        private void IniciarEdicaoHorario(Label label, string diaSemana)
        {
            if (!modoEdicao || label.Tag == null)
            {
                if (!modoEdicao)
                {
                    MessageBox.Show("Clique no botão 'Editar' primeiro para ativar o modo de edição.",
                                   "Modo Edição", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                return;
            }

            DisponibilidadeInfo info = label.Tag as DisponibilidadeInfo;
            if (info == null) return;

            // Criar TextBox para edição
            TextBox txtEdicao = new TextBox();
            txtEdicao.Text = info.TemDisponibilidade ?
                $"{info.HorarioInicio?.ToString(@"hh\:mm")} - {info.HorarioFim?.ToString(@"hh\:mm")}" :
                "";
            txtEdicao.Size = label.Size;
            txtEdicao.Location = label.Location;
            txtEdicao.Font = label.Font;
            txtEdicao.Tag = new { Label = label, DiaSemana = diaSemana, Info = info };
            txtEdicao.PlaceholderText = "Ex: 19:00 - 20:30 ou 21:00 - 23:00";

            // Eventos do TextBox
            txtEdicao.KeyDown += (s, e) =>
            {
                if (e.KeyCode == Keys.Enter)
                {
                    FinalizarEdicaoHorario(txtEdicao);
                }
                else if (e.KeyCode == Keys.Escape)
                {
                    CancelarEdicao(txtEdicao);
                }
            };

            txtEdicao.LostFocus += (s, e) => FinalizarEdicaoHorario(txtEdicao);

            // Adicionar TextBox ao painel na mesma posição do label
            TableLayoutPanelCellPosition posicao = pnl_disponibilidade.GetCellPosition(label);
            pnl_disponibilidade.Controls.Add(txtEdicao, posicao.Column, posicao.Row);

            // Ocultar label e focar no TextBox
            label.Visible = false;
            txtEdicao.Focus();
            txtEdicao.SelectAll();

            textBoxesEdicao.Add(txtEdicao);
        }

        /// <summary>
        /// Finaliza a edição de horário
        /// </summary>
        private void FinalizarEdicaoHorario(TextBox txtEdicao)
        {
            if (txtEdicao.Tag == null) return;

            dynamic tagInfo = txtEdicao.Tag;
            Label label = tagInfo.Label;
            string diaSemana = tagInfo.DiaSemana;
            DisponibilidadeInfo info = tagInfo.Info;

            string textoHorario = txtEdicao.Text.Trim();

            if (string.IsNullOrEmpty(textoHorario))
            {
                // Remover disponibilidade
                info.HorarioInicio = null;
                info.HorarioFim = null;
                label.Text = "Não disponível";
            }
            else
            {
                // Validar e parsear horário no formato "HH:mm - HH:mm"
                if (ValidarEParsearHorario(textoHorario, out TimeSpan inicio, out TimeSpan fim))
                {
                    info.HorarioInicio = inicio;
                    info.HorarioFim = fim;
                    label.Text = $"{inicio:hh\\:mm} - {fim:hh\\:mm}";
                }
                else
                {
                    MessageBox.Show("Formato de horário inválido. Use o formato 'HH:mm - HH:mm' (ex: 19:00 - 20:30).",
                                   "Erro", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEdicao.Focus();
                    return;
                }
            }

            // Remover TextBox e mostrar label
            pnl_disponibilidade.Controls.Remove(txtEdicao);
            label.Visible = true;
            textBoxesEdicao.Remove(txtEdicao);
            txtEdicao.Dispose();
        }

        /// <summary>
        /// Valida e parseia horário no formato "HH:mm - HH:mm"
        /// </summary>
        private bool ValidarEParsearHorario(string texto, out TimeSpan inicio, out TimeSpan fim)
        {
            inicio = TimeSpan.Zero;
            fim = TimeSpan.Zero;

            if (string.IsNullOrWhiteSpace(texto))
                return false;

            string[] partes = texto.Split('-');
            if (partes.Length != 2)
                return false;

            string inicioStr = partes[0].Trim();
            string fimStr = partes[1].Trim();

            if (!TimeSpan.TryParse(inicioStr, out inicio) || !TimeSpan.TryParse(fimStr, out fim))
                return false;

            if (fim <= inicio)
            {
                MessageBox.Show("O horário de fim deve ser maior que o horário de início.",
                               "Horário Inválido", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            // Validar se os horários estão dentro dos períodos permitidos
            if (!ValidarHorariosPermitidos(inicio, fim))
            {
                MessageBox.Show("Os horários de disponibilidade devem ser:\n\n" +
                               "• 19:00 às 20:30\n" +
                               "• 21:00 às 23:00\n\n" +
                               "Por favor, selecione um dos horários permitidos.",
                               "Horários Não Permitidos", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Valida se os horários estão dentro dos períodos permitidos
        /// </summary>
        private bool ValidarHorariosPermitidos(TimeSpan inicio, TimeSpan fim)
        {
            // Definir os períodos permitidos
            TimeSpan periodo1_inicio = new TimeSpan(19, 0, 0);  // 19:00
            TimeSpan periodo1_fim = new TimeSpan(20, 30, 0);    // 20:30
            TimeSpan periodo2_inicio = new TimeSpan(21, 0, 0);  // 21:00
            TimeSpan periodo2_fim = new TimeSpan(23, 0, 0);     // 23:00

            // Verificar se corresponde exatamente ao primeiro período (19:00 - 20:30)
            bool periodo1_valido = (inicio == periodo1_inicio && fim == periodo1_fim);

            // Verificar se corresponde exatamente ao segundo período (21:00 - 23:00)
            bool periodo2_valido = (inicio == periodo2_inicio && fim == periodo2_fim);

            // Retornar true se corresponde a qualquer um dos períodos permitidos
            return periodo1_valido || periodo2_valido;
        }


        /// <summary>
        /// Ativa o modo de edição
        /// </summary>
        private void btn_editar_disponibilidade_Click(object sender, EventArgs e)
        {
            modoEdicao = true;
            btn_salvar_disponibilidade.Visible = true;
            MessageBox.Show("Modo de edição ativado!\n\n" +
                           "• Dê duplo clique nos horários para editá-los\n" +
                           "• Use o formato: 19:00 - 20:30 ou 21:00 - 23:00\n" +
                           "• Deixe em branco para remover disponibilidade\n" +
                           "• Clique em 'Salvar' para confirmar as alterações",
                           "Modo Edição", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// Salva todas as alterações realizadas
        /// </summary>
        private void btn_salvar_disponibilidade_Click(object sender, EventArgs e)
        {
            try
            {
                if (!SessaoUsuario.EhProfessor || !SessaoUsuario.IdProfessor.HasValue)
                {
                    MessageBox.Show("Erro: Professor não identificado.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                int alteracoes = 0;
                var erros = new List<string>();

                foreach (var kvp in disponibilidadesPorDia)
                {
                    string dia = kvp.Key;
                    DisponibilidadeInfo info = kvp.Value;

                    try
                    {
                        if (info.TemDisponibilidade && info.HorarioInicio.HasValue && info.HorarioFim.HasValue)
                        {
                            // Atualizar disponibilidade existente
                            cmdBanco.AtualizarDisponibilidadeProfessor(info.IdDisponibilidade, dia,
                                                                     info.HorarioInicio.Value, info.HorarioFim.Value);
                            alteracoes++;
                        }
                        else if (info.TemDisponibilidade && (!info.HorarioInicio.HasValue || !info.HorarioFim.HasValue))
                        {
                            // Excluir disponibilidade (foi removida na edição)
                            cmdBanco.ExcluirDisponibilidadeProfessor(SessaoUsuario.IdProfessor.Value, info.IdDisponibilidade);
                            alteracoes++;
                        }
                        else if (!info.TemDisponibilidade && info.HorarioInicio.HasValue && info.HorarioFim.HasValue)
                        {
                            // Criar nova disponibilidade
                            variaveisbanco dados = new variaveisbanco
                            {
                                diasemana = dia,
                                horarioinicio = info.HorarioInicio.Value,
                                horariofim = info.HorarioFim.Value
                            };
                            cmdBanco.InserirDisponibilidadeComVinculo(dados, SessaoUsuario.IdProfessor.Value);
                            alteracoes++;
                        }
                    }
                    catch (Exception ex)
                    {
                        erros.Add($"Erro ao salvar {dia}: {ex.Message}");
                    }
                }

                // Mensagem de resultado
                if (erros.Count == 0)
                {
                    string mensagem = alteracoes == 0 ? "Nenhuma alteração foi detectada." :
                                     alteracoes == 1 ? "1 alteração salva com sucesso!" :
                                     $"{alteracoes} alterações salvas com sucesso!";

                    MessageBox.Show(mensagem, "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    string mensagem = $"{alteracoes} alterações salvas.\n\nErros encontrados:\n{string.Join("\n", erros)}";
                    MessageBox.Show(mensagem, "Resultado", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // Desativar modo edição e recarregar dados
                modoEdicao = false;
                btn_salvar_disponibilidade.Visible = false;
                CarregarDisponibilidades();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao salvar alterações: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Exclui disponibilidades selecionadas
        /// </summary>
        private void btn_excluir_disponibilidade_Click(object sender, EventArgs e)
        {
            try
            {
                if (!SessaoUsuario.EhProfessor || !SessaoUsuario.IdProfessor.HasValue)
                {
                    MessageBox.Show("Erro: Professor não identificado.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Verificar quais checkboxes estão selecionados
                var diasSelecionados = new List<string>();
                foreach (var kvp in checkBoxesDias)
                {
                    if (kvp.Value.Checked && kvp.Value.Enabled)
                    {
                        diasSelecionados.Add(kvp.Key);
                    }
                }

                if (diasSelecionados.Count == 0)
                {
                    MessageBox.Show("Selecione pelo menos um dia para excluir a disponibilidade.",
                                   "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Obter informações para confirmação
                var descricoes = diasSelecionados
                    .Where(dia => disponibilidadesPorDia.ContainsKey(dia) && disponibilidadesPorDia[dia].TemDisponibilidade)
                    .Select(dia => $"{dia} ({disponibilidadesPorDia[dia].HorarioCompleto})")
                    .ToList();

                if (descricoes.Count == 0)
                {
                    MessageBox.Show("Nenhum dos dias selecionados possui disponibilidade para excluir.",
                                   "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                string mensagemConfirmacao = descricoes.Count == 1
                    ? $"Deseja realmente excluir a disponibilidade de '{descricoes[0]}'?"
                    : $"Deseja realmente excluir as disponibilidades de:\n\n{string.Join("\n", descricoes)}";

                // Confirmação do usuário
                DialogResult resultado = MessageBox.Show(mensagemConfirmacao, "Confirmar Exclusão",
                                                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (resultado == DialogResult.Yes)
                {
                    int exclusoes = 0;
                    var erros = new List<string>();

                    foreach (string dia in diasSelecionados)
                    {
                        if (disponibilidadesPorDia.ContainsKey(dia) && disponibilidadesPorDia[dia].TemDisponibilidade)
                        {
                            try
                            {
                                DisponibilidadeInfo info = disponibilidadesPorDia[dia];
                                cmdBanco.ExcluirDisponibilidadeProfessor(SessaoUsuario.IdProfessor.Value, info.IdDisponibilidade);
                                exclusoes++;
                            }
                            catch (Exception ex)
                            {
                                erros.Add($"Erro ao excluir {dia}: {ex.Message}");
                            }
                        }
                    }

                    // Mensagem de resultado
                    if (erros.Count == 0)
                    {
                        string mensagem = exclusoes == 1 ? "Disponibilidade excluída com sucesso!" :
                                         $"{exclusoes} disponibilidades excluídas com sucesso!";
                        MessageBox.Show(mensagem, "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        string mensagem = $"{exclusoes} disponibilidades excluídas.\n\nErros:\n{string.Join("\n", erros)}";
                        MessageBox.Show(mensagem, "Resultado", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }

                    // Recarregar dados
                    CarregarDisponibilidades();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao excluir disponibilidades: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


        private void btn_editar_disponibilidade_Click(object sender, EventArgs e)
        {
            modoEdicao = true;
            btn_salvar_disponibilidade.Visible = true;
            MessageBox.Show("Modo de edição ativado. Dê duplo clique nos campos para editá-los.", "Modo Edição", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void IniciarEdicao(Label label, string campo)
        {
            if (!modoEdicao || label.Tag == null) return;

            // Criar TextBox para edição
            TextBox txtEdicao = new TextBox();
            txtEdicao.Text = label.Text;
            txtEdicao.Size = label.Size;
            txtEdicao.Location = label.Location;
            txtEdicao.Font = label.Font;
            txtEdicao.Tag = new { Label = label, Campo = campo, Row = label.Tag };

            // Eventos do TextBox
            txtEdicao.KeyDown += (s, e) =>
            {
                if (e.KeyCode == Keys.Enter)
                {
                    FinalizarEdicao(txtEdicao);
                }
                else if (e.KeyCode == Keys.Escape)
                {
                    CancelarEdicao(txtEdicao);
                }
            };

            txtEdicao.LostFocus += (s, e) => FinalizarEdicao(txtEdicao);

            // Adicionar TextBox ao painel na mesma posição do label
            TableLayoutPanelCellPosition posicao = pnl_disponibilidade.GetCellPosition(label);
            pnl_disponibilidade.Controls.Add(txtEdicao, posicao.Column, posicao.Row);

            // Ocultar label e focar no TextBox
            label.Visible = false;
            txtEdicao.Focus();
            txtEdicao.SelectAll();

            textBoxesEdicao.Add(txtEdicao);
        }

        private void FinalizarEdicao(TextBox txtEdicao)
        {
            if (txtEdicao.Tag == null) return;

            dynamic tagInfo = txtEdicao.Tag;
            Label label = tagInfo.Label;
            string campo = tagInfo.Campo;
            DataRow row = tagInfo.Row;

            if (campo == "HORARIO_INICIO" || campo == "HORARIO_FIM")
            {
                // Validar formato de horário HH:mm:ss ou HH:mm
                if (TimeSpan.TryParse(txtEdicao.Text, out TimeSpan horario))
                {
                    row[campo] = horario;
                    label.Text = horario.ToString(@"hh\:mm"); // exibir no formato "HH:mm"
                }
                else
                {
                    MessageBox.Show($"O campo {campo} deve conter um horário válido (ex: 08:30).", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEdicao.Focus();
                    return;
                }
            }
            else if (campo == "DIA_SEMANA")
            {
                //valida se o dia é válido
                if (string.IsNullOrWhiteSpace(txtEdicao.Text))
                {
                    MessageBox.Show("O campo Dia da Semana não pode estar vazio.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEdicao.Focus();
                    return;
                }

                row[campo] = txtEdicao.Text.Trim();
                label.Text = txtEdicao.Text.Trim();
            }
            else
            {

                row[campo] = txtEdicao.Text;
                label.Text = txtEdicao.Text;
            }

            // Remover TextBox e mostrar label
            pnl_disponibilidade.Controls.Remove(txtEdicao);
            label.Visible = true;
            textBoxesEdicao.Remove(txtEdicao);
            txtEdicao.Dispose();
        }


        private void CancelarEdicao(TextBox txtEdicao)
        {
            if (txtEdicao.Tag == null) return;

            dynamic tagInfo = txtEdicao.Tag;
            Label label = tagInfo.Label;

            pnl_disponibilidade.Controls.Remove(txtEdicao);
            label.Visible = true;
            textBoxesEdicao.Remove(txtEdicao);
            txtEdicao.Dispose();
        }

        private void btn_voltar_visualizardisponibilidade_Click(object sender, EventArgs e)
        {
            ProjetoIntegrador.Professor.P_TelaPrincipal.P_TelaPrincipal telaPrincipal = new ProjetoIntegrador.Professor.P_TelaPrincipal.P_TelaPrincipal();
            telaPrincipal.Show();
            this.Hide();
        }
    }
}
