using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using ProjetoIntegrador.Classes;

namespace ProjetoIntegrador.Professor.P_Disponibilidade
{
    public partial class P_VisualizarDisponibilidade : Form
    {
        private comandosbanco cmdBanco = new comandosbanco();
        private bool modoEdicao = false;
        private List<TextBox> textBoxesEdicao = new List<TextBox>();
        private List<Label> labelsHorarios = new List<Label>();
        private Dictionary<string, DisponibilidadeInfo> disponibilidadesPorDia = new Dictionary<string, DisponibilidadeInfo>();

        // Classe para armazenar informações de disponibilidade
        private class DisponibilidadeInfo
        {
            public int IdDisponibilidade { get; set; }
            public string DiaSemana { get; set; }
            public TimeSpan? HorarioInicio { get; set; }
            public TimeSpan? HorarioFim { get; set; }
            public bool TemDisponibilidade => IdDisponibilidade > 0;
            public string HorarioCompleto => TemDisponibilidade && HorarioInicio.HasValue && HorarioFim.HasValue
                ? $"{HorarioInicio.Value:hh\\:mm} - {HorarioFim.Value:hh\\:mm}"
                : "Não disponível";
        }

        public P_VisualizarDisponibilidade()
        {
            InitializeComponent();
            InicializarComponentes();
            CarregarDisponibilidades();
        }

        private void InicializarComponentes()
        {
            // Ocultar checkboxes (não serão usadas neste layout)
            chb_linha1_visualizardisponibilidade.Visible = false;
            chb_linha2_visualizardisponibilidade.Visible = false;
            chb_linha3_visualizardisponibilidade.Visible = false;
            chb_linha4_visualizardisponibilidade.Visible = false;
            chb_linha5_visualizardisponibilidade.Visible = false;

            // Criar labels para exibir horários
            CriarLabelsHorarios();

            // Configurar botão salvar como invisível inicialmente
            btn_salvar_disponibilidade.Visible = false;
        }

        /// <summary>
        /// Cria labels para exibir horários na segunda coluna
        /// </summary>
        private void CriarLabelsHorarios()
        {
            var diasSemana = new[] { "Segunda-feira", "Terça-feira", "Quarta-feira", "Quinta-feira", "Sexta-feira" };

            for (int i = 0; i < diasSemana.Length; i++)
            {
                Label lblHorario = new Label();
                lblHorario.AutoSize = false;
                lblHorario.Size = new Size(280, 40);
                lblHorario.TextAlign = ContentAlignment.MiddleCenter;
                lblHorario.BackColor = Color.Transparent;
                lblHorario.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
                lblHorario.Text = "Não disponível";
                lblHorario.Tag = diasSemana[i]; // Armazenar o dia da semana no Tag
                lblHorario.DoubleClick += (s, e) => IniciarEdicaoHorario(s as Label, diasSemana[i]);

                // Adicionar na segunda coluna (índice 1) e linha correspondente (i + 1)
                pnl_disponibilidade.Controls.Add(lblHorario, 1, i + 1);
                labelsHorarios.Add(lblHorario);
            }
        }

        /// <summary>
        /// Carrega as disponibilidades do professor logado
        /// </summary>
        private void CarregarDisponibilidades()
        {
            try
            {
                // Verificar se há professor logado
                if (SessaoUsuario.IdProfessor == null)
                {
                    MessageBox.Show("Nenhum professor logado encontrado.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Buscar disponibilidades do professor
                DataTable dtDisponibilidades = cmdBanco.ConsultarDisponibilidadesPorProfessor(SessaoUsuario.IdProfessor.Value);

                // Limpar dados anteriores
                disponibilidadesPorDia.Clear();

                // Inicializar todos os dias como não disponíveis
                var diasSemana = new[] { "Segunda-feira", "Terça-feira", "Quarta-feira", "Quinta-feira", "Sexta-feira" };
                foreach (var dia in diasSemana)
                {
                    disponibilidadesPorDia[dia] = new DisponibilidadeInfo
                    {
                        IdDisponibilidade = 0,
                        DiaSemana = dia,
                        HorarioInicio = null,
                        HorarioFim = null
                    };
                }

                // Preencher com dados do banco
                foreach (DataRow row in dtDisponibilidades.Rows)
                {
                    string diaSemana = row["DIA_SEMANA"].ToString();
                    if (disponibilidadesPorDia.ContainsKey(diaSemana))
                    {
                        disponibilidadesPorDia[diaSemana] = new DisponibilidadeInfo
                        {
                            IdDisponibilidade = Convert.ToInt32(row["ID_DISPONIBILIDADE"]),
                            DiaSemana = diaSemana,
                            HorarioInicio = TimeSpan.Parse(row["HORARIO_INICIO"].ToString()),
                            HorarioFim = TimeSpan.Parse(row["HORARIO_FIM"].ToString())
                        };
                    }
                }

                // Atualizar exibição
                AtualizarExibicaoHorarios();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar disponibilidades: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Atualiza a exibição dos horários nos labels
        /// </summary>
        private void AtualizarExibicaoHorarios()
        {
            var diasSemana = new[] { "Segunda-feira", "Terça-feira", "Quarta-feira", "Quinta-feira", "Sexta-feira" };

            for (int i = 0; i < diasSemana.Length && i < labelsHorarios.Count; i++)
            {
                string dia = diasSemana[i];
                if (disponibilidadesPorDia.ContainsKey(dia))
                {
                    labelsHorarios[i].Text = disponibilidadesPorDia[dia].HorarioCompleto;
                    labelsHorarios[i].Tag = disponibilidadesPorDia[dia];
                }
                else
                {
                    labelsHorarios[i].Text = "Não disponível";
                    labelsHorarios[i].Tag = null;
                }
            }
        }

        private void P_VisualizarDisponibilidade_Load(object sender, EventArgs e)
        {
            CarregarDisponibilidades();
        }

        private void btn_fechar_visualizardisponibilidades_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        private void btn_minimizar_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_Click(object sender, EventArgs e)
        {
            ToggleMaximizeWindow();
        }

        private void ToggleMaximizeWindow()
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                this.WindowState = FormWindowState.Normal;
                this.StartPosition = FormStartPosition.CenterScreen;
                this.CenterToScreen();
            }
            else
            {
                this.WindowState = FormWindowState.Maximized;
            }
        }

        private void btn_voltar_visualizardisponibilidade_Click(object sender, EventArgs e)
        {
            ProjetoIntegrador.Professor.P_TelaPrincipal.P_TelaPrincipal telaPrincipal = new ProjetoIntegrador.Professor.P_TelaPrincipal.P_TelaPrincipal();
            telaPrincipal.Show();
            this.Hide();
        }

        /// <summary>
        /// Inicia a edição de horário para um dia específico
        /// </summary>
        private void IniciarEdicaoHorario(Label label, string diaSemana)
        {
            if (!modoEdicao)
            {
                MessageBox.Show("Clique no botão 'Editar' primeiro para ativar o modo de edição.",
                               "Modo Edição", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // Criar TextBox para edição
            TextBox txtEdicao = new TextBox();
            txtEdicao.Text = label.Text == "Não disponível" ? "" : label.Text;
            txtEdicao.Size = label.Size;
            txtEdicao.Location = label.Location;
            txtEdicao.Font = label.Font;
            txtEdicao.Tag = new { Label = label, DiaSemana = diaSemana };
            txtEdicao.PlaceholderText = "Ex: 19:00 - 20:30 ou 21:00 - 23:00";

            // Eventos do TextBox
            txtEdicao.KeyDown += (s, e) =>
            {
                if (e.KeyCode == Keys.Enter)
                {
                    FinalizarEdicaoHorario(txtEdicao);
                }
                else if (e.KeyCode == Keys.Escape)
                {
                    CancelarEdicao(txtEdicao);
                }
            };

            txtEdicao.LostFocus += (s, e) => FinalizarEdicaoHorario(txtEdicao);

            // Adicionar TextBox ao painel na mesma posição do label
            TableLayoutPanelCellPosition posicao = pnl_disponibilidade.GetCellPosition(label);
            pnl_disponibilidade.Controls.Add(txtEdicao, posicao.Column, posicao.Row);

            // Ocultar label e focar no TextBox
            label.Visible = false;
            txtEdicao.Focus();
            txtEdicao.SelectAll();

            textBoxesEdicao.Add(txtEdicao);
        }

        /// <summary>
        /// Finaliza a edição de horário
        /// </summary>
        private void FinalizarEdicaoHorario(TextBox txtEdicao)
        {
            if (txtEdicao.Tag == null) return;

            dynamic tagInfo = txtEdicao.Tag;
            Label label = tagInfo.Label;
            string diaSemana = tagInfo.DiaSemana;

            string textoHorario = txtEdicao.Text.Trim();

            if (string.IsNullOrEmpty(textoHorario))
            {
                // Remover disponibilidade
                if (disponibilidadesPorDia.ContainsKey(diaSemana))
                {
                    disponibilidadesPorDia[diaSemana].HorarioInicio = null;
                    disponibilidadesPorDia[diaSemana].HorarioFim = null;
                }
                label.Text = "Não disponível";
            }
            else
            {
                // Validar e parsear horário no formato "HH:mm - HH:mm"
                if (ValidarEParsearHorario(textoHorario, out TimeSpan inicio, out TimeSpan fim))
                {
                    if (disponibilidadesPorDia.ContainsKey(diaSemana))
                    {
                        disponibilidadesPorDia[diaSemana].HorarioInicio = inicio;
                        disponibilidadesPorDia[diaSemana].HorarioFim = fim;
                    }
                    label.Text = $"{inicio:hh\\:mm} - {fim:hh\\:mm}";
                }
                else
                {
                    MessageBox.Show("Formato de horário inválido. Use o formato 'HH:mm - HH:mm' (ex: 19:00 - 20:30).",
                                   "Erro", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEdicao.Focus();
                    return;
                }
            }

            // Remover TextBox e mostrar label
            pnl_disponibilidade.Controls.Remove(txtEdicao);
            label.Visible = true;
            textBoxesEdicao.Remove(txtEdicao);
            txtEdicao.Dispose();
        }

        /// <summary>
        /// Cancela a edição atual
        /// </summary>
        private void CancelarEdicao(TextBox txtEdicao)
        {
            if (txtEdicao.Tag == null) return;

            dynamic tagInfo = txtEdicao.Tag;
            Label label = tagInfo.Label;

            pnl_disponibilidade.Controls.Remove(txtEdicao);
            label.Visible = true;
            textBoxesEdicao.Remove(txtEdicao);
            txtEdicao.Dispose();
        }

        /// <summary>
        /// Valida e parseia horário no formato "HH:mm - HH:mm"
        /// </summary>
        private bool ValidarEParsearHorario(string texto, out TimeSpan inicio, out TimeSpan fim)
        {
            inicio = TimeSpan.Zero;
            fim = TimeSpan.Zero;

            if (string.IsNullOrWhiteSpace(texto))
                return false;

            string[] partes = texto.Split('-');
            if (partes.Length != 2)
                return false;

            string inicioStr = partes[0].Trim();
            string fimStr = partes[1].Trim();

            if (!TimeSpan.TryParse(inicioStr, out inicio) || !TimeSpan.TryParse(fimStr, out fim))
                return false;

            if (fim <= inicio)
            {
                MessageBox.Show("O horário de fim deve ser maior que o horário de início.",
                               "Horário Inválido", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            // Validar se os horários estão dentro dos períodos permitidos
            if (!ValidarHorariosPermitidos(inicio, fim))
            {
                MessageBox.Show("Os horários de disponibilidade devem ser:\n\n" +
                               "• 19:00 às 20:30\n" +
                               "• 21:00 às 23:00\n\n" +
                               "Por favor, selecione um dos horários permitidos.",
                               "Horários Não Permitidos", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Valida se os horários estão dentro dos períodos permitidos
        /// </summary>
        private bool ValidarHorariosPermitidos(TimeSpan inicio, TimeSpan fim)
        {
            // Definir os períodos permitidos
            TimeSpan periodo1_inicio = new TimeSpan(19, 0, 0);  // 19:00
            TimeSpan periodo1_fim = new TimeSpan(20, 30, 0);    // 20:30
            TimeSpan periodo2_inicio = new TimeSpan(21, 0, 0);  // 21:00
            TimeSpan periodo2_fim = new TimeSpan(23, 0, 0);     // 23:00

            // Verificar se corresponde exatamente ao primeiro período (19:00 - 20:30)
            bool periodo1_valido = (inicio == periodo1_inicio && fim == periodo1_fim);

            // Verificar se corresponde exatamente ao segundo período (21:00 - 23:00)
            bool periodo2_valido = (inicio == periodo2_inicio && fim == periodo2_fim);

            // Retornar true se corresponde a qualquer um dos períodos permitidos
            return periodo1_valido || periodo2_valido;
        }

        /// <summary>
        /// Ativa o modo de edição
        /// </summary>
        private void btn_editar_disponibilidade_Click(object sender, EventArgs e)
        {
            modoEdicao = true;
            btn_salvar_disponibilidade.Visible = true;
            MessageBox.Show("Modo de edição ativado!\n\n" +
                           "• Dê duplo clique nos horários para editá-los\n" +
                           "• Use o formato: 19:00 - 20:30 ou 21:00 - 23:00\n" +
                           "• Deixe em branco para remover disponibilidade\n" +
                           "• Clique em 'Salvar' para confirmar as alterações",
                           "Modo Edição", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// Salva todas as alterações realizadas
        /// </summary>
        private void btn_salvar_disponibilidade_Click(object sender, EventArgs e)
        {
            try
            {
                if (SessaoUsuario.IdProfessor == null)
                {
                    MessageBox.Show("Erro: Professor não identificado.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                int alteracoes = 0;
                var erros = new List<string>();

                foreach (var kvp in disponibilidadesPorDia)
                {
                    string dia = kvp.Key;
                    DisponibilidadeInfo info = kvp.Value;

                    try
                    {
                        if (info.TemDisponibilidade && info.HorarioInicio.HasValue && info.HorarioFim.HasValue)
                        {
                            // Atualizar disponibilidade existente
                            cmdBanco.AtualizarDisponibilidadeProfessor(info.IdDisponibilidade, dia,
                                                                     info.HorarioInicio.Value, info.HorarioFim.Value);
                            alteracoes++;
                        }
                        else if (info.TemDisponibilidade && (!info.HorarioInicio.HasValue || !info.HorarioFim.HasValue))
                        {
                            // Excluir disponibilidade (foi removida na edição)
                            cmdBanco.ExcluirDisponibilidadeProfessor(SessaoUsuario.IdProfessor.Value, info.IdDisponibilidade);
                            alteracoes++;
                        }
                        else if (!info.TemDisponibilidade && info.HorarioInicio.HasValue && info.HorarioFim.HasValue)
                        {
                            // Criar nova disponibilidade
                            variaveisbanco dados = new variaveisbanco
                            {
                                diasemana = dia,
                                horarioinicio = info.HorarioInicio.Value,
                                horariofim = info.HorarioFim.Value
                            };
                            cmdBanco.InserirDisponibilidadeComVinculo(dados, SessaoUsuario.IdProfessor.Value);
                            alteracoes++;
                        }
                    }
                    catch (Exception ex)
                    {
                        erros.Add($"Erro ao salvar {dia}: {ex.Message}");
                    }
                }

                // Mensagem de resultado
                if (erros.Count == 0)
                {
                    string mensagem = alteracoes == 0 ? "Nenhuma alteração foi detectada." :
                                     alteracoes == 1 ? "1 alteração salva com sucesso!" :
                                     $"{alteracoes} alterações salvas com sucesso!";

                    MessageBox.Show(mensagem, "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    string mensagem = $"{alteracoes} alterações salvas.\n\nErros encontrados:\n{string.Join("\n", erros)}";
                    MessageBox.Show(mensagem, "Resultado", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // Desativar modo edição e recarregar dados
                modoEdicao = false;
                btn_salvar_disponibilidade.Visible = false;
                CarregarDisponibilidades();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao salvar alterações: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Exclui disponibilidades selecionadas (usando checkboxes)
        /// </summary>
        private void btn_excluir_disponibilidade_Click(object sender, EventArgs e)
        {
            try
            {
                if (SessaoUsuario.IdProfessor == null)
                {
                    MessageBox.Show("Erro: Professor não identificado.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Verificar quais checkboxes estão selecionados
                var diasSelecionados = new List<string>();

                // Mapear checkboxes para dias da semana
                var checkboxMapping = new Dictionary<CheckBox, string>
                {
                    { chb_linha1_visualizardisponibilidade, "Segunda-feira" },
                    { chb_linha2_visualizardisponibilidade, "Terça-feira" },
                    { chb_linha3_visualizardisponibilidade, "Quarta-feira" },
                    { chb_linha4_visualizardisponibilidade, "Quinta-feira" },
                    { chb_linha5_visualizardisponibilidade, "Sexta-feira" }
                };

                foreach (var kvp in checkboxMapping)
                {
                    if (kvp.Key.Checked)
                    {
                        diasSelecionados.Add(kvp.Value);
                    }
                }

                if (diasSelecionados.Count == 0)
                {
                    MessageBox.Show("Selecione pelo menos um dia para excluir a disponibilidade.",
                                   "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Obter informações para confirmação
                var descricoes = diasSelecionados
                    .Where(dia => disponibilidadesPorDia.ContainsKey(dia) && disponibilidadesPorDia[dia].TemDisponibilidade)
                    .Select(dia => $"{dia} ({disponibilidadesPorDia[dia].HorarioCompleto})")
                    .ToList();

                if (descricoes.Count == 0)
                {
                    MessageBox.Show("Nenhum dos dias selecionados possui disponibilidade para excluir.",
                                   "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                string mensagemConfirmacao = descricoes.Count == 1
                    ? $"Deseja realmente excluir a disponibilidade de '{descricoes[0]}'?"
                    : $"Deseja realmente excluir as disponibilidades de:\n\n{string.Join("\n", descricoes)}";

                // Confirmação do usuário
                DialogResult resultado = MessageBox.Show(mensagemConfirmacao, "Confirmar Exclusão",
                                                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (resultado == DialogResult.Yes)
                {
                    int exclusoes = 0;
                    var erros = new List<string>();

                    foreach (string dia in diasSelecionados)
                    {
                        if (disponibilidadesPorDia.ContainsKey(dia) && disponibilidadesPorDia[dia].TemDisponibilidade)
                        {
                            try
                            {
                                DisponibilidadeInfo info = disponibilidadesPorDia[dia];
                                cmdBanco.ExcluirDisponibilidadeProfessor(SessaoUsuario.IdProfessor.Value, info.IdDisponibilidade);
                                exclusoes++;
                            }
                            catch (Exception ex)
                            {
                                erros.Add($"Erro ao excluir {dia}: {ex.Message}");
                            }
                        }
                    }

                    // Mensagem de resultado
                    if (erros.Count == 0)
                    {
                        string mensagem = exclusoes == 1 ? "Disponibilidade excluída com sucesso!" :
                                         $"{exclusoes} disponibilidades excluídas com sucesso!";
                        MessageBox.Show(mensagem, "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        string mensagem = $"{exclusoes} disponibilidades excluídas.\n\nErros:\n{string.Join("\n", erros)}";
                        MessageBox.Show(mensagem, "Resultado", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }

                    // Recarregar dados
                    CarregarDisponibilidades();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao excluir disponibilidades: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
