using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using ProjetoIntegrador.Classes;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Button;

namespace ProjetoIntegrador.Professor.P_Disponibilidade
{
    public partial class P_VisualizarDisponibilidade : Form
    {

        private List<Label> labelsDia = new List<Label>();
        private List<Label> labelsInicio = new List<Label>();
        private List<Label> labelsFim = new List<Label>();
        private DataTable dadosDisponibilidades;
        private int paginaAtual = 0;
        private int totalPaginas = 0;
        private int registrosPorPagina = 5;
        private List<TextBox> textBoxesEdicao = new List<TextBox>();
        private bool modoEdicao = false;
        private List<System.Windows.Forms.CheckBox> checkBoxes = new List<System.Windows.Forms.CheckBox>();
        private comandosbanco cmdBanco = new comandosbanco();

        public P_VisualizarDisponibilidade()
        {
            InitializeComponent();
            InicializarComponentes();
            this.Load += P_VisualizarDisponibilidade_Load;
        }

        private void btn_fechar_visualizardisponibilidades_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto


        }

        private void btn_minimizar_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_Click(object sender, EventArgs e)
        {
            ToggleMaximizeWindow();
        }

        /// <summary>
        /// Alterna entre maximizar e restaurar a janela, centralizando quando restaura
        /// </summary>
        private void ToggleMaximizeWindow()
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                // Se está maximizada, restaura ao tamanho normal e centraliza
                this.WindowState = FormWindowState.Normal;
                this.StartPosition = FormStartPosition.CenterScreen;
                this.CenterToScreen();
            }
            else
            {
                // Se não está maximizada, maximiza
                this.WindowState = FormWindowState.Maximized;
            }
        }

        /// <summary>
        /// Inicializa os componentes necessários para o funcionamento do formulário
        /// </summary>
        private void InicializarComponentes()
        {
            // Criar checkboxes dinamicamente
            CriarCheckboxes();

            // Criar labels para exibir dados
            CriarLabelsParaDados();
        }

        /// <summary>
        /// Cria checkboxes dinâmicos para seleção de registros
        /// </summary>
        private void CriarCheckboxes()
        {
            for (int i = 0; i < registrosPorPagina; i++)
            {
                System.Windows.Forms.CheckBox cb = new System.Windows.Forms.CheckBox();
                cb.AutoSize = true;
                cb.BackColor = Color.Transparent;
                cb.Name = $"cb_linha{i + 1}_disponibilidade";
                cb.Size = new Size(15, 14);
                cb.TabIndex = i + 50;
                cb.UseVisualStyleBackColor = false;
                cb.Visible = false;

                // Adicionar checkbox ao painel na primeira coluna (antes dos dados de dia)
                // Posicionar na linha i+1 (pula o cabeçalho), coluna 0
                pnl_disponibilidade.Controls.Add(cb, 0, i + 1);
                checkBoxes.Add(cb);
            }
        }

        /// <summary>
        /// Cria labels dinâmicos para exibir os dados de disponibilidade
        /// </summary>
        private void CriarLabelsParaDados()
        {
            for (int i = 0; i < registrosPorPagina; i++)
            {
                // Labels para Dia da Semana (coluna 0, mas deslocado para dar espaço ao checkbox)
                Label lblDia = new Label();
                lblDia.AutoSize = false;
                lblDia.Size = new Size(200, 40);
                lblDia.TextAlign = ContentAlignment.MiddleLeft;
                lblDia.Padding = new Padding(25, 0, 0, 0);
                lblDia.BackColor = Color.Transparent;
                lblDia.Font = new Font("Segoe UI", 10F, FontStyle.Regular);
                lblDia.ForeColor = Color.MidnightBlue;
                lblDia.DoubleClick += (s, e) => IniciarEdicao(s as Label, "DIA_SEMANA");
                lblDia.Visible = false;

                // Adicionar ao painel na mesma célula do checkbox, mas com margem
                pnl_disponibilidade.Controls.Add(lblDia, 0, i + 1);
                labelsDia.Add(lblDia);

                // Labels para Horário (coluna 1) - combinando início e fim
                Label lblHorario = new Label();
                lblHorario.AutoSize = false;
                lblHorario.Size = new Size(250, 40);
                lblHorario.TextAlign = ContentAlignment.MiddleCenter;
                lblHorario.BackColor = Color.Transparent;
                lblHorario.Font = new Font("Segoe UI", 10F, FontStyle.Regular);
                lblHorario.ForeColor = Color.MidnightBlue;
                lblHorario.Visible = false;

                pnl_disponibilidade.Controls.Add(lblHorario, 1, i + 1);

                // Para manter compatibilidade, vamos usar o mesmo label para início e fim
                labelsInicio.Add(lblHorario);
                labelsFim.Add(lblHorario); // Mesmo label para ambos
            }
        }

        private void P_VisualizarDisponibilidade_Load(object sender, EventArgs e)
        {
            CarregarDisponibilidades();
        }

        private void btn_excluir_disponibilidade_Click(object sender, EventArgs e)
        {
            // Verificar se as listas foram inicializadas
            if (checkBoxes == null || checkBoxes.Count == 0)
            {
                MessageBox.Show("Erro: Componentes não foram inicializados corretamente.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // Verificar se há checkboxes selecionados
            var checkboxesSelecionados = checkBoxes.Where(cb => cb != null && cb.Visible && cb.Checked).ToList();

            if (checkboxesSelecionados.Count == 0)
            {
                MessageBox.Show("Selecione pelo menos uma disponibilidade para excluir.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Obter os dados para confirmação
            var descricoes = checkboxesSelecionados
                .Select(cb =>
                {
                    DataRow row = (DataRow)cb.Tag;
                    string dia = row["DIA_SEMANA"].ToString();
                    string inicio = row["HORARIO_INICIO"].ToString();
                    string fim = row["HORARIO_FIM"].ToString();
                    return $"{dia} ({inicio} - {fim})";
                })
                .ToList();

            string mensagemConfirmacao;
            if (descricoes.Count == 1)
            {
                mensagemConfirmacao = $"Deseja realmente excluir a disponibilidade '{descricoes[0]}'?";
            }
            else
            {
                mensagemConfirmacao = $"Deseja realmente excluir {descricoes.Count} disponibilidades selecionadas?\n\n" +
                                      string.Join("\n", descricoes.Take(5)) +
                                      (descricoes.Count > 5 ? "\n..." : "");
            }

            // Confirmação do usuário
            DialogResult resultado = MessageBox.Show(
                mensagemConfirmacao,
                "Confirmar Exclusão",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                int disponExcluidas = 0;
                var erros = new List<string>();

                foreach (var checkbox in checkboxesSelecionados)
                {
                    DataRow row = (DataRow)checkbox.Tag;
                    int idDisponibilidade = Convert.ToInt32(row["ID_DISPONIBILIDADE"]);

                    string descricao = $"{row["DIA_SEMANA"]} ({row["HORARIO_INICIO"]} - {row["HORARIO_FIM"]})";

                    try
                    {
                        cmdBanco.DeletarDisponibilidade(idDisponibilidade);
                        disponExcluidas++;
                    }
                    catch (Exception ex)
                    {
                        erros.Add($"Erro ao excluir '{descricao}': {ex.Message}");
                    }
                }

                // Mensagem final
                if (erros.Count == 0)
                {
                    string mensagemSucesso = disponExcluidas == 1
                        ? "Disponibilidade excluída com sucesso!"
                        : $"{disponExcluidas} disponibilidades excluídas com sucesso!";

                    MessageBox.Show(mensagemSucesso, "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    string mensagemErro = $"{disponExcluidas} disponibilidades excluídas com sucesso.\n\n" +
                                          $"Erros encontrados:\n{string.Join("\n", erros)}";
                    MessageBox.Show(mensagemErro, "Resultado da Exclusão", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // Recarrega os dados na tela
                CarregarDisponibilidades();
            }
        }


        private void CarregarDisponibilidades()
        {
            try
            {
                dadosDisponibilidades = cmdBanco.ConsultarDisponibilidades();
                totalPaginas = (int)Math.Ceiling((double)dadosDisponibilidades.Rows.Count / registrosPorPagina);
                if (totalPaginas == 0) totalPaginas = 1;

                ExibirDadosPagina(); // Certifique-se de que esse método também trata disponibilidade
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar disponibilidades: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExibirDadosPagina()
        {
            // Verificar se as listas foram inicializadas
            if (labelsDia == null || labelsInicio == null || labelsFim == null || checkBoxes == null)
            {
                MessageBox.Show("Erro: Componentes não foram inicializados corretamente.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // Limpa os labels de disponibilidade
            foreach (var label in labelsDia)
            {
                if (label != null)
                {
                    label.Text = "";
                    label.Tag = null;
                    label.Visible = false;
                }
            }

            // Oculta os checkboxes
            foreach (var cb in checkBoxes)
            {
                if (cb != null)
                {
                    cb.Visible = false;
                    cb.Checked = false;
                }
            }

            if (dadosDisponibilidades == null || dadosDisponibilidades.Rows.Count == 0)
                return;

            int inicioIndice = paginaAtual * registrosPorPagina;
            int fimIndice = Math.Min(inicioIndice + registrosPorPagina, dadosDisponibilidades.Rows.Count);

            for (int i = inicioIndice; i < fimIndice; i++)
            {
                int indiceLabel = i - inicioIndice;

                // Verificar se o índice está dentro dos limites
                if (indiceLabel >= checkBoxes.Count || indiceLabel >= labelsDia.Count || indiceLabel >= labelsInicio.Count)
                    continue;

                DataRow row = dadosDisponibilidades.Rows[i];

                // Exibir checkbox
                if (checkBoxes[indiceLabel] != null)
                {
                    checkBoxes[indiceLabel].Visible = true;
                    checkBoxes[indiceLabel].Tag = row;
                }

                // Preencher labels com dados da disponibilidade
                if (labelsDia[indiceLabel] != null)
                {
                    labelsDia[indiceLabel].Text = row["DIA_SEMANA"].ToString();
                    labelsDia[indiceLabel].Tag = row;
                    labelsDia[indiceLabel].Visible = true;
                }

                // Combinar horário início e fim em um só label
                if (labelsInicio[indiceLabel] != null)
                {
                    string horarioInicio = "";
                    string horarioFim = "";

                    if (TimeSpan.TryParse(row["HORARIO_INICIO"].ToString(), out TimeSpan inicio))
                        horarioInicio = inicio.ToString(@"hh\:mm");

                    if (TimeSpan.TryParse(row["HORARIO_FIM"].ToString(), out TimeSpan fim))
                        horarioFim = fim.ToString(@"hh\:mm");

                    labelsInicio[indiceLabel].Text = $"{horarioInicio} - {horarioFim}";
                    labelsInicio[indiceLabel].Tag = row;
                    labelsInicio[indiceLabel].Visible = true;
                }
            }
        }


        private void btn_editar_disponibilidade_Click(object sender, EventArgs e)
        {
            modoEdicao = true;
            btn_salvar_disponibilidade.Visible = true;
            MessageBox.Show("Modo de edição ativado. Dê duplo clique nos campos para editá-los.", "Modo Edição", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void IniciarEdicao(Label label, string campo)
        {
            if (!modoEdicao || label.Tag == null) return;

            // Criar TextBox para edição
            TextBox txtEdicao = new TextBox();
            txtEdicao.Text = label.Text;
            txtEdicao.Size = label.Size;
            txtEdicao.Location = label.Location;
            txtEdicao.Font = label.Font;
            txtEdicao.Tag = new { Label = label, Campo = campo, Row = label.Tag };

            // Eventos do TextBox
            txtEdicao.KeyDown += (s, e) =>
            {
                if (e.KeyCode == Keys.Enter)
                {
                    FinalizarEdicao(txtEdicao);
                }
                else if (e.KeyCode == Keys.Escape)
                {
                    CancelarEdicao(txtEdicao);
                }
            };

            txtEdicao.LostFocus += (s, e) => FinalizarEdicao(txtEdicao);

            // Adicionar TextBox ao painel na mesma posição do label
            TableLayoutPanelCellPosition posicao = pnl_disponibilidade.GetCellPosition(label);
            pnl_disponibilidade.Controls.Add(txtEdicao, posicao.Column, posicao.Row);

            // Ocultar label e focar no TextBox
            label.Visible = false;
            txtEdicao.Focus();
            txtEdicao.SelectAll();

            textBoxesEdicao.Add(txtEdicao);
        }

        private void FinalizarEdicao(TextBox txtEdicao)
        {
            if (txtEdicao.Tag == null) return;

            dynamic tagInfo = txtEdicao.Tag;
            Label label = tagInfo.Label;
            string campo = tagInfo.Campo;
            DataRow row = tagInfo.Row;

            if (campo == "HORARIO_INICIO" || campo == "HORARIO_FIM")
            {
                // Validar formato de horário HH:mm:ss ou HH:mm
                if (TimeSpan.TryParse(txtEdicao.Text, out TimeSpan horario))
                {
                    row[campo] = horario;
                    label.Text = horario.ToString(@"hh\:mm"); // exibir no formato "HH:mm"
                }
                else
                {
                    MessageBox.Show($"O campo {campo} deve conter um horário válido (ex: 08:30).", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEdicao.Focus();
                    return;
                }
            }
            else if (campo == "DIA_SEMANA")
            {
                //valida se o dia é válido
                if (string.IsNullOrWhiteSpace(txtEdicao.Text))
                {
                    MessageBox.Show("O campo Dia da Semana não pode estar vazio.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEdicao.Focus();
                    return;
                }

                row[campo] = txtEdicao.Text.Trim();
                label.Text = txtEdicao.Text.Trim();
            }
            else
            {

                row[campo] = txtEdicao.Text;
                label.Text = txtEdicao.Text;
            }

            // Remover TextBox e mostrar label
            pnl_disponibilidade.Controls.Remove(txtEdicao);
            label.Visible = true;
            textBoxesEdicao.Remove(txtEdicao);
            txtEdicao.Dispose();
        }


        private void CancelarEdicao(TextBox txtEdicao)
        {
            if (txtEdicao.Tag == null) return;

            dynamic tagInfo = txtEdicao.Tag;
            Label label = tagInfo.Label;

            pnl_disponibilidade.Controls.Remove(txtEdicao);
            label.Visible = true;
            textBoxesEdicao.Remove(txtEdicao);
            txtEdicao.Dispose();
        }

        private void btn_voltar_visualizardisponibilidade_Click(object sender, EventArgs e)
        {
            ProjetoIntegrador.Professor.P_TelaPrincipal.P_TelaPrincipal telaPrincipal = new ProjetoIntegrador.Professor.P_TelaPrincipal.P_TelaPrincipal();
            telaPrincipal.Show();
            this.Hide();
        }
    }
}
