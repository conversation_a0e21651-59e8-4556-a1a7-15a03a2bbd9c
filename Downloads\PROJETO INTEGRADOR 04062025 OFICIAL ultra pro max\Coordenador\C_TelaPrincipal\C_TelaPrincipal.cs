﻿using System;
using System.Linq;
using System.Windows.Forms;

namespace ProjetoIntegrador.Coordenador.C_TelaPrincipal
{
    public partial class C_TelaPrincipal : Form
    {
        public C_TelaPrincipal()
        {
            InitializeComponent();
        }

        private void AbrirTela(Form novaTela)
        {
            bool estaMaximizado = this.WindowState == FormWindowState.Maximized;

            this.Hide();
            if (estaMaximizado)
                novaTela.WindowState = FormWindowState.Maximized;

            novaTela.FormClosed += (s, args) =>
            {
                this.Show();
                if (estaMaximizado)
                    this.WindowState = FormWindowState.Maximized;
            };

            novaTela.Show();
        }

        private void btn_professores_telaprincipal_Click(object sender, EventArgs e)
        {
            AbrirTela(new ProjetoIntegrador.C_Professor.C_Professor());
        }

        private void btn_visualizarprofessores_telaprincipal_Click(object sender, EventArgs e)
        {
            AbrirTela(new ProjetoIntegrador.Coordenador.C_VisualizarProfessores.C_VisualizarProfessores());
        }

        private void btn_visualizardisciplinas_telaprincipal_Click(object sender, EventArgs e)
        {
            AbrirTela(new ProjetoIntegrador.Coordenador.C_VisualizarDisciplinas.C_VisualizarDisciplinas());
        }

        private void btn_disponibilidade_telaprincipal_Click(object sender, EventArgs e)
        {
            AbrirTela(new ProjetoIntegrador.Coordenador.C_Disponibilidade.C_Disponibilidade());
        }

        private void btn_conferirgrade_telaprincipal_Click(object sender, EventArgs e)
        {
            AbrirTela(new ProjetoIntegrador.Coordenador.C_ConferirGrade2.C_ConferirGrade2());
        }

        private void btn_disciplinas_telaprincipal_Click(object sender, EventArgs e)
        {
            AbrirTela(new ProjetoIntegrador.C_Disciplina.C_IDisciplina());
        }

        private void btn_matrizescurriculares_telaprincipal_Click(object sender, EventArgs e)
        {
            AbrirTela(new ProjetoIntegrador.C_matrizescurriculares.C_MatrizesCurriculares());
        }

        private void btn_maximizar_telaprincipal_Click(object sender, EventArgs e)
        {
            ToggleMaximizeWindow();
        }

        /// <summary>
        /// Alterna entre maximizar e restaurar a janela, centralizando quando restaura
        /// </summary>
        private void ToggleMaximizeWindow()
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                // Se está maximizada, restaura ao tamanho normal e centraliza
                this.WindowState = FormWindowState.Normal;
                this.StartPosition = FormStartPosition.CenterScreen;
                this.CenterToScreen();
            }
            else
            {
                // Se não está maximizada, maximiza
                this.WindowState = FormWindowState.Maximized;
            }
        }

        private void btn_minimizar_telaprincipal_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_fechar_disciplina_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            Application.Exit(); // Fecha a tela principal
        }

        private void btn_fechar_telaprincipal_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto

        }
    }
}
