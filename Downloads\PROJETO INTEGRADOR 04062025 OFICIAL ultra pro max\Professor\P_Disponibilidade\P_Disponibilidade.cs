using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using MySql.Data.MySqlClient;
using ProjetoIntegrador.Classes;

namespace ProjetoIntegrador.Professor
{
    public partial class P_IDisponibilidade : Form
    {

        public P_IDisponibilidade()
        {
            InitializeComponent();
            CarregarDiasSemana();
            ConfigurarEventosHorarios();
        }

        // Método para carregar os dias da semana no ComboBox
        private void CarregarDiasSemana()
        {
            try
            {
                // Limpa o ComboBox
                ComboBox cbDias = Controls.Find("cb_selecionardias", true).FirstOrDefault() as ComboBox;

                if (cbDias != null)
                {
                    cbDias.Items.Clear();

                    // Adiciona um item padrão
                    cbDias.Items.Add("Selecione um dia...");

                    // Adiciona os dias da semana
                    cbDias.Items.Add("Segunda-feira");
                    cbDias.Items.Add("Terça-feira");
                    cbDias.Items.Add("Quarta-feira");
                    cbDias.Items.Add("Quinta-feira");
                    cbDias.Items.Add("Sexta-feira");
                    cbDias.Items.Add("Sábado");

                    // Seleciona o item padrão
                    cbDias.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Erro ao carregar dias da semana: " + ex.Message, "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btn_fechar_Click(object sender, EventArgs e)
        {
            DialogResult resultado = MessageBox.Show(
                "Deseja realmente fechar a aplicação?",
                "Confirmação",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (resultado == DialogResult.Yes)
            {
                Application.Exit(); // Fecha a tela de disciplina
            }
            // Se for "No", nada acontece e o formulário continua aberto


        }

        private void btn_minimizar_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
        }

        private void btn_maximizar_Click(object sender, EventArgs e)
        {
            ToggleMaximizeWindow();
        }

        /// <summary>
        /// Alterna entre maximizar e restaurar a janela, centralizando quando restaura
        /// </summary>
        private void ToggleMaximizeWindow()
        {
            if (this.WindowState == FormWindowState.Maximized)
            {
                // Se está maximizada, restaura ao tamanho normal e centraliza
                this.WindowState = FormWindowState.Normal;
                this.StartPosition = FormStartPosition.CenterScreen;
                this.CenterToScreen();
            }
            else
            {
                // Se não está maximizada, maximiza
                this.WindowState = FormWindowState.Maximized;
            }
        }

        private void btn_inserir_Click(object sender, EventArgs e)
        {
            // Validar se todos os campos estão preenchidos
            if (!ValidarCampos())
            {
                return;
            }

            try
            {
                // Capturar os valores dos campos
                string diaSemana = cb_selecionardias_disponibilidade.Text;
                string horarioInicio = txt_horarioinicio_disponibilidade.Text;
                string horarioFim = txt_horariofim_disponibilidade.Text;

                // Validar formato dos horários
                if (!TimeSpan.TryParse(horarioInicio, out TimeSpan inicio) ||
                    !TimeSpan.TryParse(horarioFim, out TimeSpan fim))
                {
                    MessageBox.Show("Por favor, insira os horários no formato correto (HH:MM).\n" +
                                   "Exemplo: 08:00 ou 14:30",
                                   "Formato Inválido", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Validar se horário de fim é maior que horário de início
                if (fim <= inicio)
                {
                    MessageBox.Show("O horário de fim deve ser maior que o horário de início.",
                                   "Horário Inválido", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Validar se os horários estão dentro dos períodos permitidos
                if (!ValidarHorariosPermitidos(inicio, fim))
                {
                    MessageBox.Show("Os horários de disponibilidade devem ser:\n\n" +
                                   "• 19:00 às 20:30\n" +
                                   "• 21:00 às 23:00\n\n" +
                                   "Por favor, selecione um dos horários permitidos.",
                                   "Horários Não Permitidos", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Preencher instância da classe VariaveisBanco
                variaveisbanco dados = new variaveisbanco
                {
                    diasemana = diaSemana,
                    horarioinicio = inicio,
                    horariofim = fim
                };

                // Chamar o método InserirDisponibilidade
                comandosbanco cmdBanco = new comandosbanco();
                bool sucesso = cmdBanco.InserirDisponibilidade(dados);

                if (sucesso)
                {
                    // Exibir MessageBox de sucesso
                    MessageBox.Show($"Disponibilidade cadastrada com sucesso!\n\n" +
                                   $"📅 Dia: {dados.diasemana}\n" +
                                   $"🕐 Início: {dados.horarioinicio:hh\\:mm}\n" +
                                   $"🕐 Fim: {dados.horariofim:hh\\:mm}",
                                   "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Limpar campos após cadastro bem-sucedido
                    LimparCampos();
                }
                else
                {
                    // Exibir MessageBox de erro
                    MessageBox.Show("Erro ao cadastrar disponibilidade.\n" +
                                   "Verifique os dados e tente novamente.",
                                   "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (MySqlException sqlEx)
            {
                string mensagemErro = "Erro no banco de dados:\n";
                if (sqlEx.Message.Contains("Duplicate entry"))
                    mensagemErro += "Já existe uma disponibilidade igual cadastrada.";
                else
                    mensagemErro += sqlEx.Message;

                MessageBox.Show(mensagemErro, "Erro de Banco de Dados", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro inesperado ao cadastrar disponibilidade:\n{ex.Message}",
                               "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Valida se todos os campos obrigatórios estão preenchidos
        /// </summary>
        /// <returns>True se todos os campos estão válidos, False caso contrário</returns>
        private bool ValidarCampos()
        {
            // Validar ComboBox de dias
            if (cb_selecionardias_disponibilidade.SelectedIndex == -1)
            {
                MessageBox.Show("Por favor, selecione um dia da semana.",
                               "Campo Obrigatório", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cb_selecionardias_disponibilidade.Focus();
                return false;
            }

            // Validar horário de início
            if (string.IsNullOrWhiteSpace(txt_horarioinicio_disponibilidade.Text))
            {
                MessageBox.Show("Por favor, informe o horário de início.",
                               "Campo Obrigatório", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txt_horarioinicio_disponibilidade.Focus();
                return false;
            }

            // Validar horário de fim
            if (string.IsNullOrWhiteSpace(txt_horariofim_disponibilidade.Text))
            {
                MessageBox.Show("Por favor, informe o horário de fim.",
                               "Campo Obrigatório", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txt_horariofim_disponibilidade.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// Limpa todos os campos do formulário
        /// </summary>
        private void LimparCampos()
        {
            txt_horarioinicio_disponibilidade.Text = "";
            txt_horariofim_disponibilidade.Text = "";
            cb_selecionardias_disponibilidade.SelectedIndex = -1;

            // Focar no primeiro campo para facilitar nova entrada
            cb_selecionardias_disponibilidade.Focus();
        }

        /// <summary>
        /// Configura eventos para facilitar a entrada de horários
        /// </summary>
        private void ConfigurarEventosHorarios()
        {
            // Adicionar eventos de clique para sugerir horários
            txt_horarioinicio_disponibilidade.Enter += txt_horarioinicio_disponibilidade_Enter;
            txt_horariofim_disponibilidade.Enter += txt_horariofim_disponibilidade_Enter;

            // Adicionar eventos para validar formato durante a digitação
            txt_horarioinicio_disponibilidade.Leave += txt_horarioinicio_disponibilidade_Leave;
            txt_horariofim_disponibilidade.Leave += txt_horariofim_disponibilidade_Leave;
        }

        /// <summary>
        /// Evento quando o campo de horário de início recebe foco
        /// </summary>
        private void txt_horarioinicio_disponibilidade_Enter(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txt_horarioinicio_disponibilidade.Text))
            {
                // Mostrar tooltip com horários permitidos
                ToolTip tooltip = new ToolTip();
                tooltip.SetToolTip(txt_horarioinicio_disponibilidade,
                    "Horários permitidos:\n• 19:00 (para período 19:00-20:30)\n• 21:00 (para período 21:00-23:00)");
            }
        }

        /// <summary>
        /// Evento quando o campo de horário de fim recebe foco
        /// </summary>
        private void txt_horariofim_disponibilidade_Enter(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txt_horariofim_disponibilidade.Text))
            {
                // Mostrar tooltip com horários permitidos
                ToolTip tooltip = new ToolTip();
                tooltip.SetToolTip(txt_horariofim_disponibilidade,
                    "Horários permitidos:\n• 20:30 (para período 19:00-20:30)\n• 23:00 (para período 21:00-23:00)");
            }
        }

        /// <summary>
        /// Evento quando o campo de horário de início perde foco
        /// </summary>
        private void txt_horarioinicio_disponibilidade_Leave(object sender, EventArgs e)
        {
            string horario = txt_horarioinicio_disponibilidade.Text.Trim();

            // Auto-completar horário de fim baseado no horário de início
            if (horario == "19:00" || horario == "19")
            {
                txt_horarioinicio_disponibilidade.Text = "19:00";
                if (string.IsNullOrWhiteSpace(txt_horariofim_disponibilidade.Text))
                {
                    txt_horariofim_disponibilidade.Text = "20:30";
                }
            }
            else if (horario == "21:00" || horario == "21")
            {
                txt_horarioinicio_disponibilidade.Text = "21:00";
                if (string.IsNullOrWhiteSpace(txt_horariofim_disponibilidade.Text))
                {
                    txt_horariofim_disponibilidade.Text = "23:00";
                }
            }
        }

        /// <summary>
        /// Evento quando o campo de horário de fim perde foco
        /// </summary>
        private void txt_horariofim_disponibilidade_Leave(object sender, EventArgs e)
        {
            string horario = txt_horariofim_disponibilidade.Text.Trim();

            // Auto-completar horário de início baseado no horário de fim
            if (horario == "20:30")
            {
                txt_horariofim_disponibilidade.Text = "20:30";
                if (string.IsNullOrWhiteSpace(txt_horarioinicio_disponibilidade.Text))
                {
                    txt_horarioinicio_disponibilidade.Text = "19:00";
                }
            }
            else if (horario == "23:00" || horario == "23")
            {
                txt_horariofim_disponibilidade.Text = "23:00";
                if (string.IsNullOrWhiteSpace(txt_horarioinicio_disponibilidade.Text))
                {
                    txt_horarioinicio_disponibilidade.Text = "21:00";
                }
            }
        }

        /// <summary>
        /// Valida se os horários estão dentro dos períodos permitidos
        /// Períodos permitidos: 19:00-20:30 ou 21:00-23:00
        /// </summary>
        /// <param name="inicio">Horário de início</param>
        /// <param name="fim">Horário de fim</param>
        /// <returns>True se os horários são válidos, False caso contrário</returns>
        private bool ValidarHorariosPermitidos(TimeSpan inicio, TimeSpan fim)
        {
            // Definir os períodos permitidos
            TimeSpan periodo1_inicio = new TimeSpan(19, 0, 0);  // 19:00
            TimeSpan periodo1_fim = new TimeSpan(20, 30, 0);    // 20:30

            TimeSpan periodo2_inicio = new TimeSpan(21, 0, 0);  // 21:00
            TimeSpan periodo2_fim = new TimeSpan(23, 0, 0);     // 23:00

            // Verificar se corresponde exatamente ao primeiro período (19:00 - 20:30)
            bool periodo1_valido = (inicio == periodo1_inicio && fim == periodo1_fim);

            // Verificar se corresponde exatamente ao segundo período (21:00 - 23:00)
            bool periodo2_valido = (inicio == periodo2_inicio && fim == periodo2_fim);

            // Retornar true se corresponde a qualquer um dos períodos permitidos
            return periodo1_valido || periodo2_valido;
        }

        private void P_IDisponibilidade_Load(object sender, EventArgs e)
        {
            // Preencher o ComboBox com os dias da semana
            PreencherComboBoxDias();

            // Mostrar informação sobre horários permitidos ao carregar a tela
            MessageBox.Show("📋 HORÁRIOS DE DISPONIBILIDADE PERMITIDOS:\n\n" +
                           "🕐 Período 1: 19:00 às 20:30\n" +
                           "🕐 Período 2: 21:00 às 23:00\n\n" +
                           "💡 Dica: Digite apenas o horário de início (19 ou 21) que o sistema completará automaticamente!",
                           "Informações Importantes", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// Preenche o ComboBox com os dias da semana
        /// </summary>
        private void PreencherComboBoxDias()
        {
            try
            {
                // Limpar o ComboBox antes de preencher
                cb_selecionardias_disponibilidade.Items.Clear();

                // Adicionar os dias da semana conforme solicitado
                cb_selecionardias_disponibilidade.Items.Add("Segunda-feira");
                cb_selecionardias_disponibilidade.Items.Add("Terça-feira");
                cb_selecionardias_disponibilidade.Items.Add("Quarta-feira");
                cb_selecionardias_disponibilidade.Items.Add("Quinta-feira");
                cb_selecionardias_disponibilidade.Items.Add("Sexta-feira");

                // Configurar propriedades do ComboBox
                cb_selecionardias_disponibilidade.DropDownStyle = ComboBoxStyle.DropDownList; // Impede edição manual
                cb_selecionardias_disponibilidade.SelectedIndex = -1; // Nenhum item selecionado inicialmente
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao carregar dias da semana: {ex.Message}",
                               "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btn_voltar_disponibilidade_Click(object sender, EventArgs e)
        {
            ProjetoIntegrador.Professor.P_TelaPrincipal.P_TelaPrincipal telaPrincipal = new ProjetoIntegrador.Professor.P_TelaPrincipal.P_TelaPrincipal();
            telaPrincipal.Show();
            this.Hide();
        }
    }
}
